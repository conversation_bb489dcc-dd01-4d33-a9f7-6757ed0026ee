name: sba
description: "SBA Mobile Application"

publish_to: 'none'

version: 0.0.4+10

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.17
  go_router: ^14.8.0
  elementary: ^3.2.0
  elementary_helper: ^1.0.2
  flutter_form_builder: ^9.7.0
  form_builder_image_picker: ^4.2.0
  form_builder_extra_fields: ^11.1.0
  form_builder_validators: ^11.1.1
  gap: ^3.0.1
  collection: ^1.19.0
  timeago: ^3.7.0
  skeletonizer: ^1.4.3
  get_it: ^8.0.3
  latlong2: ^0.9.1
  flutter_map: ^7.0.2
  flutter_map_marker_popup: ^7.0.0
  flutter_map_location_marker: ^9.1.1
  flutter_map_animations: ^0.8.0
  modal_bottom_sheet: ^3.0.0
  retrofit: ^4.4.2
  logger: ^2.5.0
  dio: ^5.7.0
  json_annotation: ^4.9.0
  rxdart: ^0.28.0
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.1
  memory_cache: ^1.2.0
  location_picker_flutter_map: ^3.1.0
  flutter_carousel_widget: ^3.1.0
  sliver_tools: ^0.2.12
  rx_shared_preferences: ^4.0.0
  barcode_widget: ^2.0.4
  time_range_picker: ^2.3.0
  vector_graphics: any
  intl: any
  toastification: ^2.3.0
  webview_flutter: ^4.10.0
  flutter_widget_from_html_core: ^0.16.0
  app_links: ^6.4.0
  uuid: ^4.5.1
  sms_sender_background: ^1.0.6
  after_layout: ^1.2.0
  expandable_page_view: ^1.0.17
  onesignal_flutter: ^5.3.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  flutter_gen_runner: ^5.8.0
  very_good_analysis: ^7.0.0
  go_router_builder: ^2.7.3
  flutter_launcher_icons: ^0.14.3
  retrofit_generator: ^9.1.7
  json_serializable: ^6.9.3

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/

  fonts:
    - family: OpenSans
      fonts:
        - asset: assets/fonts/OpenSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/OpenSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/OpenSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/OpenSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/OpenSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/OpenSans-Light.ttf
          weight: 300

flutter_gen:
  output: lib/src/generated
  line_length: 80

  integrations:
    flutter_svg: true
