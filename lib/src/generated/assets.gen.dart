/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/account_circle.svg
  SvgGenImage get accountCircle =>
      const SvgGenImage('assets/icons/account_circle.svg');

  /// File path: assets/icons/add_shopping_cart.svg
  SvgGenImage get addShoppingCart =>
      const SvgGenImage('assets/icons/add_shopping_cart.svg');

  /// File path: assets/icons/build.svg
  SvgGenImage get build => const SvgGenImage('assets/icons/build.svg');

  /// File path: assets/icons/car_crash.svg
  SvgGenImage get carCrash => const SvgGenImage('assets/icons/car_crash.svg');

  /// File path: assets/icons/change_circle.svg
  SvgGenImage get changeCircle =>
      const SvgGenImage('assets/icons/change_circle.svg');

  /// File path: assets/icons/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/icons/delete.svg');

  /// File path: assets/icons/directions_car.svg
  SvgGenImage get directionsCar =>
      const SvgGenImage('assets/icons/directions_car.svg');

  /// File path: assets/icons/engine.svg
  SvgGenImage get engine => const SvgGenImage('assets/icons/engine.svg');

  /// File path: assets/icons/exit_app.svg
  SvgGenImage get exitApp => const SvgGenImage('assets/icons/exit_app.svg');

  /// File path: assets/icons/featured_play_list.svg
  SvgGenImage get featuredPlayList =>
      const SvgGenImage('assets/icons/featured_play_list.svg');

  /// File path: assets/icons/gavel.svg
  SvgGenImage get gavel => const SvgGenImage('assets/icons/gavel.svg');

  /// File path: assets/icons/gearbox.svg
  SvgGenImage get gearbox => const SvgGenImage('assets/icons/gearbox.svg');

  /// File path: assets/icons/home.svg
  SvgGenImage get home => const SvgGenImage('assets/icons/home.svg');

  /// File path: assets/icons/information.svg
  SvgGenImage get information =>
      const SvgGenImage('assets/icons/information.svg');

  /// File path: assets/icons/local_gas_station.svg
  SvgGenImage get localGasStation =>
      const SvgGenImage('assets/icons/local_gas_station.svg');

  /// File path: assets/icons/local_parking.svg
  SvgGenImage get localParking =>
      const SvgGenImage('assets/icons/local_parking.svg');

  /// File path: assets/icons/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/icons/notification.svg');

  /// File path: assets/icons/other.svg
  SvgGenImage get other => const SvgGenImage('assets/icons/other.svg');

  /// File path: assets/icons/partners.svg
  SvgGenImage get partners => const SvgGenImage('assets/icons/partners.svg');

  /// File path: assets/icons/person.svg
  SvgGenImage get person => const SvgGenImage('assets/icons/person.svg');

  /// File path: assets/icons/person_add.svg
  SvgGenImage get personAdd => const SvgGenImage('assets/icons/person_add.svg');

  /// File path: assets/icons/protect.svg
  SvgGenImage get protect => const SvgGenImage('assets/icons/protect.svg');

  /// File path: assets/icons/rv_hookup.svg
  SvgGenImage get rvHookup => const SvgGenImage('assets/icons/rv_hookup.svg');

  /// File path: assets/icons/school.svg
  SvgGenImage get school => const SvgGenImage('assets/icons/school.svg');

  /// File path: assets/icons/search_check.svg
  SvgGenImage get searchCheck =>
      const SvgGenImage('assets/icons/search_check.svg');

  /// File path: assets/icons/settings.svg
  SvgGenImage get settings => const SvgGenImage('assets/icons/settings.svg');

  /// File path: assets/icons/tire.svg
  SvgGenImage get tire => const SvgGenImage('assets/icons/tire.svg');

  /// File path: assets/icons/tire_seasonal.svg
  SvgGenImage get tireSeasonal =>
      const SvgGenImage('assets/icons/tire_seasonal.svg');

  /// File path: assets/icons/tire_summer.svg
  SvgGenImage get tireSummer =>
      const SvgGenImage('assets/icons/tire_summer.svg');

  /// File path: assets/icons/tire_winter.svg
  SvgGenImage get tireWinter =>
      const SvgGenImage('assets/icons/tire_winter.svg');

  /// File path: assets/icons/toggle_on.svg
  SvgGenImage get toggleOn => const SvgGenImage('assets/icons/toggle_on.svg');

  /// File path: assets/icons/two_pager.svg
  SvgGenImage get twoPager => const SvgGenImage('assets/icons/two_pager.svg');

  /// File path: assets/icons/videocam.svg
  SvgGenImage get videocam => const SvgGenImage('assets/icons/videocam.svg');

  /// File path: assets/icons/vignette.svg
  SvgGenImage get vignette => const SvgGenImage('assets/icons/vignette.svg');

  /// File path: assets/icons/warehouse.svg
  SvgGenImage get warehouse => const SvgGenImage('assets/icons/warehouse.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        accountCircle,
        addShoppingCart,
        build,
        carCrash,
        changeCircle,
        delete,
        directionsCar,
        engine,
        exitApp,
        featuredPlayList,
        gavel,
        gearbox,
        home,
        information,
        localGasStation,
        localParking,
        notification,
        other,
        partners,
        person,
        personAdd,
        protect,
        rvHookup,
        school,
        searchCheck,
        settings,
        tire,
        tireSeasonal,
        tireSummer,
        tireWinter,
        toggleOn,
        twoPager,
        videocam,
        vignette,
        warehouse
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/app_logo.png
  AssetGenImage get appLogo =>
      const AssetGenImage('assets/images/app_logo.png');

  /// File path: assets/images/car_placeholder.jpg
  AssetGenImage get carPlaceholder =>
      const AssetGenImage('assets/images/car_placeholder.jpg');

  /// File path: assets/images/car_placeholder_wide.jpg
  AssetGenImage get carPlaceholderWide =>
      const AssetGenImage('assets/images/car_placeholder_wide.jpg');

  /// File path: assets/images/mastercard.png
  AssetGenImage get mastercard =>
      const AssetGenImage('assets/images/mastercard.png');

  /// File path: assets/images/member_gold_card.png
  AssetGenImage get memberGoldCard =>
      const AssetGenImage('assets/images/member_gold_card.png');

  /// File path: assets/images/member_platinum_card.png
  AssetGenImage get memberPlatinumCard =>
      const AssetGenImage('assets/images/member_platinum_card.png');

  /// File path: assets/images/member_silver_card.png
  AssetGenImage get memberSilverCard =>
      const AssetGenImage('assets/images/member_silver_card.png');

  /// File path: assets/images/member_silver_plus_card.png
  AssetGenImage get memberSilverPlusCard =>
      const AssetGenImage('assets/images/member_silver_plus_card.png');

  /// File path: assets/images/placeholder_image.png
  AssetGenImage get placeholderImage =>
      const AssetGenImage('assets/images/placeholder_image.png');

  /// File path: assets/images/subscription_gold_card.png
  AssetGenImage get subscriptionGoldCard =>
      const AssetGenImage('assets/images/subscription_gold_card.png');

  /// File path: assets/images/subscription_partners.png
  AssetGenImage get subscriptionPartners =>
      const AssetGenImage('assets/images/subscription_partners.png');

  /// File path: assets/images/subscription_platinum_card.png
  AssetGenImage get subscriptionPlatinumCard =>
      const AssetGenImage('assets/images/subscription_platinum_card.png');

  /// File path: assets/images/subscription_silver_card.png
  AssetGenImage get subscriptionSilverCard =>
      const AssetGenImage('assets/images/subscription_silver_card.png');

  /// File path: assets/images/subscription_silver_plus_card.png
  AssetGenImage get subscriptionSilverPlusCard =>
      const AssetGenImage('assets/images/subscription_silver_plus_card.png');

  /// File path: assets/images/training_center_image.png
  AssetGenImage get trainingCenterImage =>
      const AssetGenImage('assets/images/training_center_image.png');

  /// File path: assets/images/visa.png
  AssetGenImage get visa => const AssetGenImage('assets/images/visa.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        appLogo,
        carPlaceholder,
        carPlaceholderWide,
        mastercard,
        memberGoldCard,
        memberPlatinumCard,
        memberSilverCard,
        memberSilverPlusCard,
        placeholderImage,
        subscriptionGoldCard,
        subscriptionPartners,
        subscriptionPlatinumCard,
        subscriptionSilverCard,
        subscriptionSilverPlusCard,
        trainingCenterImage,
        visa
      ];
}

class Assets {
  Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
