enum PaymentStatus {
  unknown(-1),
  registered(0),
  onHold(1),
  authorized(2),
  canceled(3),
  refunded(4),
  authorizing(5),
  declined(6);

  const PaymentStatus(this.code);

  factory PaymentStatus.fromServerValue(int serverValue) =>
      PaymentStatus.values.firstWhere(
        (e) => e.code == serverValue,
        orElse: () => PaymentStatus.unknown,
      );

  final int code;
}

extension PaymentStatusExtension on PaymentStatus {
  bool get isEligibleForRetry => {
        PaymentStatus.unknown,
        PaymentStatus.registered,
        PaymentStatus.canceled,
      }.contains(this);
}
