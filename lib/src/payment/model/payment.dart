import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/payment/register_order_dto.dart';

@immutable
final class Payment {
  const Payment({
    required this.orderId,
    required this.uri,
  });

  factory Payment.fromDto(RegisterOrderDto dto) => Payment(
        orderId: dto.id,
        uri: dto.url,
      );

  final String orderId;
  final String uri;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Payment &&
          runtimeType == other.runtimeType &&
          orderId == other.orderId;

  @override
  int get hashCode => orderId.hashCode;

  Payment copyWith({
    String? orderId,
    String? uri,
  }) {
    return Payment(
      orderId: orderId ?? this.orderId,
      uri: uri ?? this.uri,
    );
  }
}
