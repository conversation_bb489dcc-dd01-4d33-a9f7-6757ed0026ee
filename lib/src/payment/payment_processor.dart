import 'dart:collection';
import 'dart:ui';

import 'package:app_links/app_links.dart';
import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/dsk_api.dart';
import 'package:sba/src/api/model/request/register_order_request_dto.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/lifecycle_observer/lifecycle_observer.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/payment/model/payment.dart';
import 'package:sba/src/payment/model/payment_status.dart';
import 'package:uuid/uuid.dart';

const _tag = 'PaymentProcessor';
const _language = 'bg';
const _currency = 975;
const _deepLinkUrl = 'sba://payment.flow';

typedef PaymentData = ({String orderId, PaymentStatus status});

final class PaymentProcessor {
  PaymentProcessor({
    required Logger logger,
    required DSKApi dskApi,
    required AppLinks appLinks,
    required LifecycleObserver lifecycleObserver,
  })  : _logger = logger,
        _dskApi = dskApi,
        _appLinks = appLinks,
        _lifecycleObserver = lifecycleObserver;
  final Logger _logger;
  final DSKApi _dskApi;
  final AppLinks _appLinks;
  final LifecycleObserver _lifecycleObserver;

  final _pendingPayments = HashMap<Payment, PaymentStatus>();

  Future<Result<PaymentData>> makePayment(int amount) async {
    try {
      final orderResult = await callWithResult(
        () => _dskApi.registerPayment(
          dto: RegisterOrderRequestDto(
            amount: amount,
            currency: _currency,
            orderNumber: const Uuid().v4(),
            returnUrl: _deepLinkUrl,
            language: _language,
          ),
        ),
      );

      final order = orderResult.map(Payment.fromDto).maybeValue;

      if (order == null) {
        return Result.paymentError();
      }

      _pendingPayments.putIfAbsent(order, () => PaymentStatus.unknown);

      return startPaymentFlow(order.orderId);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<PaymentData>> startPaymentFlow(String orderId) async {
    try {
      final entry = _pendingPayments.entries
          .firstWhereOrNull((el) => el.key.orderId == orderId);

      if (entry?.value == null || !entry!.value.isEligibleForRetry) {
        return Result.paymentError();
      }

      await ExternalLauncher.uri(
        entry.key.uri,
      );

      await Rx.merge([
        _lifecycleObserver.lifecycleStream
            .skip(1)
            .where((e) => e == AppLifecycleState.resumed)
            .doOnData((e) => _logger.d('$_tag: Lifecycle change: $e'))
            .map((e) => null),
        _appLinks.uriLinkStream
            .where((e) => e.toString().contains(_deepLinkUrl))
            .where((e) => e.queryParameters.containsValue(entry.key.orderId))
            .doOnData((e) => _logger.d('$_tag: Received link: $e')),
      ]).delay(const Duration(seconds: 2)).first;

      final statusResult = await callWithResult(
        () => _dskApi.checkOrderStatus(orderId: entry.key.orderId),
      );

      final status = statusResult.maybeValue;
      final paymentStatus = PaymentStatus.fromServerValue(
        status?.orderStatus ?? entry.value.code,
      );

      _pendingPayments[entry.key] = paymentStatus;

      if (paymentStatus == PaymentStatus.authorized) {
        _pendingPayments.remove(entry.key);
      }

      return Result.success(
        (
          orderId: entry.key.orderId,
          status: paymentStatus,
        ),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
