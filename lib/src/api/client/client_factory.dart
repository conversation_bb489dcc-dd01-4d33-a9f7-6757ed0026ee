import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:sba/src/api/interceptors/dsk_auth_interceptor.dart';
import 'package:sba/src/api/interceptors/logging_interceptor.dart';
import 'package:sba/src/api/interceptors/token_interceptor.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';

const _tag = 'HttpClient';

Dio buildClient({required Logger logger, required String baseUrl}) => Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 10),
        sendTimeout: const Duration(seconds: 5),
        headers: {Headers.contentTypeHeader: Headers.jsonContentType},
      ),
    )..interceptors.add(LoggerInterceptor(logger: logger, tag: _tag));

Dio buildAuthenticatedClient({
  required Logger logger,
  required String baseUrl,
  required AuthRepository auth,
}) {
  final dio = buildClient(logger: logger, baseUrl: baseUrl);
  dio.interceptors.add(TokenInterceptor(repository: auth, client: dio));
  return dio;
}

Dio buildPaymentClient({
  required Logger logger,
  required String baseUrl,
  required String username,
  required String password,
}) {
  final dio = buildClient(logger: logger, baseUrl: baseUrl);
  dio.interceptors.add(
    DskAuthInterceptor(
      username: username,
      password: password,
    ),
  );
  return dio;
}
