// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollDto _$TollDtoFromJson(Map<String, dynamic> json) => TollDto(
      id: (json['id'] as num?)?.toInt(),
      userId: (json['mobileUserId'] as num).toInt(),
      customer: json['customer'] as String,
      eik: json['eik'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      date: DateTime.parse(json['date'] as String),
      countryCode: json['countryCode'] as String,
      vehicleType: (json['vehicleType'] as num).toInt(),
      plateNumber: json['dkn'] as String,
      from: DateTime.parse(json['from'] as String),
      to: DateTime.parse(json['to'] as String),
      price: (json['price'] as num).toDouble(),
      state: (json['state'] as num?)?.toInt(),
      vignetteId: json['vignetteId'] as String,
      issued: json['issued'] == null
          ? null
          : DateTime.parse(json['issued'] as String),
      registerJson: _$JsonConverterFromJson<String, Map<String, dynamic>>(
          json['registerJsonResponse'], const JsonStringConverter().fromJson),
      activateJson: _$JsonConverterFromJson<String, Map<String, dynamic>>(
          json['activateJsonResponse'], const JsonStringConverter().fromJson),
      checkJson: _$JsonConverterFromJson<String, Map<String, dynamic>>(
          json['checkJsonResponse'], const JsonStringConverter().fromJson),
    );

Map<String, dynamic> _$TollDtoToJson(TollDto instance) => <String, dynamic>{
      'id': instance.id,
      'mobileUserId': instance.userId,
      'customer': instance.customer,
      'eik': instance.eik,
      'email': instance.email,
      'phone': instance.phone,
      'date': instance.date.toIso8601String(),
      'countryCode': instance.countryCode,
      'vehicleType': instance.vehicleType,
      'dkn': instance.plateNumber,
      'from': instance.from.toIso8601String(),
      'to': instance.to.toIso8601String(),
      'price': instance.price,
      'state': instance.state,
      'vignetteId': instance.vignetteId,
      'issued': instance.issued?.toIso8601String(),
      'registerJsonResponse':
          _$JsonConverterToJson<String, Map<String, dynamic>>(
              instance.registerJson, const JsonStringConverter().toJson),
      'activateJsonResponse':
          _$JsonConverterToJson<String, Map<String, dynamic>>(
              instance.activateJson, const JsonStringConverter().toJson),
      'checkJsonResponse': _$JsonConverterToJson<String, Map<String, dynamic>>(
          instance.checkJson, const JsonStringConverter().toJson),
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
