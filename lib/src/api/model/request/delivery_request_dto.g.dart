// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeliveryRequestDto _$DeliveryRequestDtoFromJson(Map<String, dynamic> json) =>
    DeliveryRequestDto(
      deliveryTo: (json['deliveryTo'] as num).toInt(),
      speedyOfficeId: (json['speedyOfficeId'] as num?)?.toInt(),
      speedyOffice: json['speedyOffice'] as String?,
      cityId: (json['cityId'] as num?)?.toInt(),
      city: json['city'] as String?,
      complexId: (json['complexId'] as num?)?.toInt(),
      complex: json['complex'] as String?,
      streetId: (json['streetId'] as num?)?.toInt(),
      street: json['street'] as String?,
      streetNumber: json['streetNumber'] as String?,
      blockId: (json['blockId'] as num?)?.toInt(),
      block: json['block'] as String?,
      entranceNumber: json['entranceNumber'] as String?,
      floor: json['floor'] as String?,
      apartmentNumber: json['apartmentNumber'] as String?,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$DeliveryRequestDtoToJson(DeliveryRequestDto instance) =>
    <String, dynamic>{
      'deliveryTo': instance.deliveryTo,
      if (instance.speedyOfficeId case final value?) 'speedyOfficeId': value,
      if (instance.speedyOffice case final value?) 'speedyOffice': value,
      if (instance.cityId case final value?) 'cityId': value,
      if (instance.city case final value?) 'city': value,
      if (instance.complexId case final value?) 'complexId': value,
      if (instance.complex case final value?) 'complex': value,
      if (instance.streetId case final value?) 'streetId': value,
      if (instance.street case final value?) 'street': value,
      if (instance.streetNumber case final value?) 'streetNumber': value,
      if (instance.blockId case final value?) 'blockId': value,
      if (instance.block case final value?) 'block': value,
      if (instance.entranceNumber case final value?) 'entranceNumber': value,
      if (instance.floor case final value?) 'floor': value,
      if (instance.apartmentNumber case final value?) 'apartmentNumber': value,
      if (instance.note case final value?) 'note': value,
    };
