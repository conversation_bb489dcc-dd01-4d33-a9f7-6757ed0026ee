// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_toll_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegisterTollRequestDto _$RegisterTollRequestDtoFromJson(
        Map<String, dynamic> json) =>
    RegisterTollRequestDto(
      activationDate: DateTime.parse(json['activationDate'] as String),
      email: json['email'] as String?,
      productId: (json['kapschProductId'] as num).toInt(),
      vehicle: _$recordConvert(
        json['vehicle'],
        ($jsonValue) => (
          countryCode: $jsonValue['countryCode'] as String,
          lpn: $jsonValue['lpn'] as String,
        ),
      ),
    );

Map<String, dynamic> _$RegisterTollRequestDtoToJson(
        RegisterTollRequestDto instance) =>
    <String, dynamic>{
      'activationDate': instance.activationDate.toIso8601String(),
      'email': instance.email,
      'kapschProductId': instance.productId,
      'vehicle': <String, dynamic>{
        'countryCode': instance.vehicle.countryCode,
        'lpn': instance.vehicle.lpn,
      },
    };

$Rec _$recordConvert<$Rec>(
  Object? value,
  $Rec Function(Map) convert,
) =>
    convert(value as Map<String, dynamic>);
