import 'package:json_annotation/json_annotation.dart';

part 'payment_status_dto.g.dart';

@JsonSerializable()
final class PaymentStatusDto {
  PaymentStatusDto({
    this.type,
    this.title,
    this.status,
    this.detail,
  });

  factory PaymentStatusDto.fromJson(Map<String, dynamic> json) =>
      _$PaymentStatusDtoFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentStatusDtoToJson(this);

  @Json<PERSON>ey(name: 'type')
  final String? type;
  @JsonKey(name: 'title')
  final String? title;
  @JsonKey(name: 'status')
  final int? status;
  @Json<PERSON>ey(name: 'detail')
  final String? detail;
}
