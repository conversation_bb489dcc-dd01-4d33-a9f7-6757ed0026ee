// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParkingRequestDto _$ParkingRequestDtoFromJson(Map<String, dynamic> json) =>
    ParkingRequestDto(
      id: (json['id'] as num?)?.toInt(),
      placeId: (json['placeId'] as num).toInt(),
      zoneId: (json['phoneId'] as num).toInt(),
      dkn: json['dkn'] as String,
      durationHours: (json['durationHours'] as num).toInt(),
      dateTime: json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
    );

Map<String, dynamic> _$ParkingRequestDtoToJson(ParkingRequestDto instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      'placeId': instance.placeId,
      'phoneId': instance.zoneId,
      'dkn': instance.dkn,
      'durationHours': instance.durationHours,
      if (instance.dateTime?.toIso8601String() case final value?)
        'created': value,
    };
