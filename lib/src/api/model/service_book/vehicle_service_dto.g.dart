// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_service_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleServiceDto _$VehicleServiceDtoFromJson(Map<String, dynamic> json) =>
    VehicleServiceDto(
      name: json['name'] as String,
      serviceDate: DateTime.parse(json['serviceDate'] as String),
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VehicleServiceDtoToJson(VehicleServiceDto instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.vehicleId case final value?) 'carId': value,
      'name': instance.name,
      'serviceDate': instance.serviceDate.toIso8601String(),
    };
