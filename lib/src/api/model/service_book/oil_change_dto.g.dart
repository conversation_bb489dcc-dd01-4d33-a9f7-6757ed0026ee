// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'oil_change_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OilChangeDto _$OilChangeDtoFromJson(Map<String, dynamic> json) => OilChangeDto(
      changeDate: DateTime.parse(json['changeDate'] as String),
      autoService: json['autoservice'] as String,
      oilType: json['oilType'] as String,
      oilQuantity: (json['oilQuantity'] as num).toDouble(),
      mileage: (json['mileage'] as num).toInt(),
      oilFilterIsChanged: json['oilFilterIsChanged'] as bool,
      airFilterIsChanged: json['airFilterIsChanged'] as bool,
      cabinFilterIsChanged: json['cabinFilterIsChanged'] as bool,
      fuelFilterIsChanged: json['fuelFilterIsChanged'] as bool,
      price: (json['price'] as num).toDouble(),
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$OilChangeDtoToJson(OilChangeDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'carId': instance.vehicleId,
      'changeDate': instance.changeDate.toIso8601String(),
      'autoservice': instance.autoService,
      'oilType': instance.oilType,
      'oilQuantity': instance.oilQuantity,
      'mileage': instance.mileage,
      'oilFilterIsChanged': instance.oilFilterIsChanged,
      'airFilterIsChanged': instance.airFilterIsChanged,
      'cabinFilterIsChanged': instance.cabinFilterIsChanged,
      'fuelFilterIsChanged': instance.fuelFilterIsChanged,
      'price': instance.price,
      'notes': instance.notes,
    };
