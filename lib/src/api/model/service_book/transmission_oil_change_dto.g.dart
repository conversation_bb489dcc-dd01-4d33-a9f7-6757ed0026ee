// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transmission_oil_change_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TransmissionOilChangeDto _$TransmissionOilChangeDtoFromJson(
        Map<String, dynamic> json) =>
    TransmissionOilChangeDto(
      changeDate: DateTime.parse(json['changeDate'] as String),
      autoService: json['autoservice'] as String,
      oilType: json['oilType'] as String,
      oilQuantity: (json['oilQuantity'] as num).toDouble(),
      mileage: (json['mileage'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$TransmissionOilChangeDtoToJson(
        TransmissionOilChangeDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'carId': instance.vehicleId,
      'changeDate': instance.changeDate.toIso8601String(),
      'autoservice': instance.autoService,
      'oilType': instance.oilType,
      'oilQuantity': instance.oilQuantity,
      'mileage': instance.mileage,
      'price': instance.price,
      'notes': instance.notes,
    };
