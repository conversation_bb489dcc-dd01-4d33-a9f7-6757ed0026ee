// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fuel_traders_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FuelTradersDto _$FuelTradersDtoFromJson(Map<String, dynamic> json) =>
    FuelTradersDto(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      fuels: (json['fuels'] as List<dynamic>)
          .map((e) => FuelDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FuelTradersDtoToJson(FuelTradersDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'fuels': instance.fuels,
    };

FuelDto _$FuelDtoFromJson(Map<String, dynamic> json) => FuelDto(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      typeId: (json['typeId'] as num).toInt(),
      type: json['type'] as String,
    );

Map<String, dynamic> _$FuelDtoToJson(FuelDto instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'typeId': instance.typeId,
      'type': instance.type,
    };
