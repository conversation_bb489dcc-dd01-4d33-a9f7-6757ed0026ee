// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'newspaper_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NewspaperDto _$NewspaperDtoFromJson(Map<String, dynamic> json) => NewspaperDto(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String?,
      year: (json['year'] as num).toInt(),
      issue: (json['issue'] as num).toInt(),
      url: json['url'] as String,
    );

Map<String, dynamic> _$NewspaperDtoToJson(NewspaperDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'year': instance.year,
      'issue': instance.issue,
      'url': instance.url,
    };
