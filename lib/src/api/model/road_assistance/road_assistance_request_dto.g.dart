// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'road_assistance_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoadAssistanceRequestDto _$RoadAssistanceRequestDtoFromJson(
        Map<String, dynamic> json) =>
    RoadAssistanceRequestDto(
      id: (json['id'] as num?)?.toInt(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      reasonId: (json['reasonId'] as num).toInt(),
      driverName: json['driverName'] as String?,
      driverPhone: json['driverPhone'] as String?,
      carMakerId: (json['carMakerId'] as num).toInt(),
      carModelId: (json['carModelId'] as num).toInt(),
      carDkn: json['carDkn'] as String,
      carTransmissionType: (json['carTransmissionType'] as num).toInt(),
      carDriveType: (json['carDriveType'] as num).toInt(),
      note: json['note'] as String?,
      created: json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
      smartViewUrl: json['smartViewUrl'] as String?,
      estimatedArrivalTime: json['estimatedArrivalTime'] == null
          ? null
          : DateTime.parse(json['estimatedArrivalTime'] as String),
      status: (json['status'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RoadAssistanceRequestDtoToJson(
        RoadAssistanceRequestDto instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.latitude case final value?) 'latitude': value,
      if (instance.longitude case final value?) 'longitude': value,
      'reasonId': instance.reasonId,
      if (instance.driverName case final value?) 'driverName': value,
      if (instance.driverPhone case final value?) 'driverPhone': value,
      'carMakerId': instance.carMakerId,
      'carModelId': instance.carModelId,
      'carDkn': instance.carDkn,
      'carTransmissionType': instance.carTransmissionType,
      'carDriveType': instance.carDriveType,
      if (instance.note case final value?) 'note': value,
      if (instance.created?.toIso8601String() case final value?)
        'created': value,
      if (instance.smartViewUrl case final value?) 'smartViewUrl': value,
      if (instance.estimatedArrivalTime?.toIso8601String() case final value?)
        'estimatedArrivalTime': value,
      if (instance.status case final value?) 'status': value,
    };
