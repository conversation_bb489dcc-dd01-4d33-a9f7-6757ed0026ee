// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_office_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeliveryOfficeDto _$DeliveryOfficeDtoFromJson(Map<String, dynamic> json) =>
    DeliveryOfficeDto(
      id: (json['id'] as num).toInt(),
      siteId: (json['siteId'] as num).toInt(),
      name: json['name'] as String,
      nameEn: json['nameEn'] as String,
      address: _$recordConvert(
        json['address'],
        ($jsonValue) => (
          fullAddressString: $jsonValue['fullAddressString'] as String,
          localAddressString: $jsonValue['localAddressString'] as String,
        ),
      ),
    );

Map<String, dynamic> _$DeliveryOfficeDtoToJson(DeliveryOfficeDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'siteId': instance.siteId,
      'name': instance.name,
      'nameEn': instance.nameEn,
      'address': <String, dynamic>{
        'fullAddressString': instance.address.fullAddressString,
        'localAddressString': instance.address.localAddressString,
      },
    };

$Rec _$recordConvert<$Rec>(
  Object? value,
  $Rec Function(Map) convert,
) =>
    convert(value as Map<String, dynamic>);
