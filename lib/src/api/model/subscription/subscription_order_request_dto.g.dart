// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_order_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionOrderRequestDto _$SubscriptionOrderRequestDtoFromJson(
        Map<String, dynamic> json) =>
    SubscriptionOrderRequestDto(
      id: (json['id'] as num?)?.toInt(),
      carId: (json['carId'] as num).toInt(),
      oldCardId: (json['oldCardId'] as num?)?.toInt(),
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      mobile: json['mobile'] as String,
      cityId: (json['cityId'] as num).toInt(),
      address: json['address'] as String,
      cardCategory: (json['cardCategory'] as num).toInt(),
      cardType: (json['cardType'] as num).toInt(),
      status: (json['status'] as num?)?.toInt(),
      note: json['note'] as String?,
      delivery:
          DeliveryRequestDto.fromJson(json['delivery'] as Map<String, dynamic>),
      selectedPackageIds: (json['selectedPackageIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$SubscriptionOrderRequestDtoToJson(
        SubscriptionOrderRequestDto instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      'carId': instance.carId,
      if (instance.oldCardId case final value?) 'oldCardId': value,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'mobile': instance.mobile,
      'cityId': instance.cityId,
      'address': instance.address,
      'cardCategory': instance.cardCategory,
      'cardType': instance.cardType,
      if (instance.status case final value?) 'status': value,
      if (instance.note case final value?) 'note': value,
      'delivery': instance.delivery,
      if (instance.selectedPackageIds case final value?)
        'selectedPackageIds': value,
    };
