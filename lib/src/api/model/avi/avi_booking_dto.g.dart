// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'avi_booking_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AVIBookingDto _$AVIBookingDtoFromJson(Map<String, dynamic> json) =>
    AVIBookingDto(
      id: json['id'] as String,
      state: json['state'] as String,
      services: (json['services'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      date: DateTime.parse(json['date'] as String),
      vehicleCategory: (json['vehicleCategory'] as num).toInt(),
      slot: json['slot'] as String,
      plateNumber: json['plateNumber'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      customerPhone: json['customerPhone'] as String,
      locationDetails:
          AVIPointDto.fromJson(json['locationDetails'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AVIBookingDtoToJson(AVIBookingDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'state': instance.state,
      'services': instance.services,
      'date': instance.date.toIso8601String(),
      'vehicleCategory': instance.vehicleCategory,
      'slot': instance.slot,
      'plateNumber': instance.plateNumber,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'customerPhone': instance.customerPhone,
      'locationDetails': instance.locationDetails,
    };
