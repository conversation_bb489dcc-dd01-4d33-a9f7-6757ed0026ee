import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:webview_flutter/webview_flutter.dart';

const _heightJavascript = 'document.documentElement.scrollHeight;';
const _head =
    '<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head>';

class AppWebview extends StatefulWidget {
  const AppWebview({
    super.key,
    this.url,
    this.html,
    this.initialHeight = 250,
    this.background,
    this.showLoader = true,
  });

  final String? url;
  final String? html;
  final Color? background;
  final double initialHeight;
  final bool showLoader;

  @override
  State<AppWebview> createState() => _AppWebviewState();
}

class _AppWebviewState extends State<AppWebview>
    with AutomaticKeepAliveClientMixin {
  bool _initialLoading = true;
  double _height = 0;

  final _controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setBackgroundColor(Colors.white);

  late final _delegate = NavigationDelegate(
    onNavigationRequest: (request) async {
      if (_initialLoading) return NavigationDecision.navigate;

      await launchUrlString(request.url);
      return NavigationDecision.prevent;
    },
    onPageFinished: (page) async {
      final height = await _controller.runJavaScriptReturningResult(
        _heightJavascript,
      ) as double?;

      if (height != null) {
        setState(() {
          _height = height;
          _initialLoading = false;
        });
      }
    },
  );

  @override
  void initState() {
    if (widget.background != null) {
      _controller.setBackgroundColor(widget.background!);
    }
    _height = widget.initialHeight;
    _controller.setNavigationDelegate(_delegate);
    _load();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AppWebview oldWidget) {
    if (oldWidget.url != widget.url || oldWidget.html != widget.html) _load();
    if (oldWidget.background != widget.background &&
        widget.background != null) {
      _controller.setBackgroundColor(widget.background!);
    }
    super.didUpdateWidget(oldWidget);
  }

  void _load() {
    if (widget.url != null) {
      _controller.loadRequest(Uri.parse(widget.url!));
      return;
    }

    if (widget.html != null) {
      _controller.loadHtmlString('$_head${widget.html}');
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SizedBox(
      width: double.maxFinite,
      height: _height,
      child: Stack(
        alignment: Alignment.center,
        children: [
          WebViewWidget(
            controller: _controller,
          ),
          AnimatedOpacity(
            opacity: _initialLoading && widget.showLoader ? 1 : 0,
            duration: const Duration(milliseconds: 500),
            child: const CircularProgressIndicator(
              strokeWidth: 6,
              strokeCap: StrokeCap.round,
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
