import 'package:flutter/material.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/components/dashed_border.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class FormBuilderCustomImagePicker extends StatelessWidget {
  const FormBuilderCustomImagePicker({
    required this.name,
    super.key,
    this.imageCount = 1,
  });

  final String name;
  final int imageCount;

  @override
  Widget build(BuildContext context) {
    return FormBuilderImagePicker(
      name: name,
      decoration: const InputDecoration(
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
      bottomSheetPadding: UISpacing.defaultScreenPadding,
      placeholderWidget: const _Placeholder(),
      cameraIcon: const Icon(Icons.camera_alt),
      galleryIcon: const Icon(Icons.photo_library),
      cameraLabel: Text(context.l10n.form_image_hint_camera),
      galleryLabel: Text(context.l10n.form_image_hint_gallery),
      maxImages: imageCount,
    );
  }
}

class _Placeholder extends StatelessWidget {
  const _Placeholder();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: UIColors.background,
        border: const DashedBorder.fromBorderSide(
          dashLength: 10,
          spaceLength: 5,
          side: BorderSide(
            color: UIColors.primary,
          ),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.upload_outlined,
            color: context.theme.colorScheme.primary,
          ),
          const Gap(UISpacing.s),
          Text(
            context.l10n.form_image_hint,
            style: context.textTheme.bodyMedium?.copyWith(
              color: UIColors.paragraph,
            ),
          ),
        ],
      ),
    );
  }
}
