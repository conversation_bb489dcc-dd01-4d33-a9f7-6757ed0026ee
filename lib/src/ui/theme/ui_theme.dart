import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';

abstract class UITheme {
  static final data = ThemeData(
    useMaterial3: true,
    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    colorScheme: const ColorScheme(
      brightness: Brightness.light,
      primary: UIColors.primary,
      onPrimary: Colors.white,
      secondary: UIColors.background,
      onSecondary: UIColors.paragraph,
      error: UIColors.red,
      onError: Colors.white,
      surface: Colors.white,
      onSurface: UIColors.paragraph,
    ),
    textTheme: const TextTheme(
      headlineLarge: UITypography.h1,
      headlineMedium: UITypography.h2,
      headlineSmall: UITypography.h3,
      titleMedium: UITypography.p,
      bodyMedium: UITypography.p,
      bodySmall: UITypography.pSmall,
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        textStyle: UITypography.button,
        backgroundColor: UIColors.primary,
        disabledBackgroundColor: UIColors.border,
        foregroundColor: Colors.white,
        minimumSize: const Size(125, 40),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        textStyle: UITypography.button,
        foregroundColor: UIColors.primary,
        disabledForegroundColor: UIColors.border,
      ),
    ),
    iconButtonTheme: IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: UIColors.primary,
        visualDensity: const VisualDensity(
          horizontal: VisualDensity.minimumDensity,
          vertical: VisualDensity.minimumDensity,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: UIColors.primary,
        disabledForegroundColor: UIColors.border,
        textStyle: UITypography.button,
        side: const BorderSide(color: UIColors.primary),
        minimumSize: const Size(125, 40),
      ),
    ),
    inputDecorationTheme: const InputDecorationTheme(
      floatingLabelBehavior: FloatingLabelBehavior.always,
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: UIColors.border),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: UIColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: UIColors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: UIColors.red, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: UIColors.border),
      ),
      hintStyle: UITypography.p,
      floatingLabelStyle: UITypography.inputLabel,
      labelStyle: UITypography.p,
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((state) {
        if (state.contains(WidgetState.selected)) {
          return UIColors.primary;
        } else {
          return UIColors.border;
        }
      }),
      visualDensity: const VisualDensity(
        horizontal: VisualDensity.minimumDensity,
        vertical: VisualDensity.minimumDensity,
      ),
    ),
    checkboxTheme: const CheckboxThemeData(
      side: BorderSide(color: UIColors.border, width: 2),
      visualDensity: VisualDensity(
        horizontal: VisualDensity.minimumDensity,
        vertical: VisualDensity.minimumDensity,
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: UIColors.menuBackground,
      selectedItemColor: UIColors.primary,
      unselectedItemColor: UIColors.menuForeground,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      unselectedLabelStyle: UITypography.menuLabel,
      selectedLabelStyle: UITypography.selectedMenuLabel,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    badgeTheme: const BadgeThemeData(
      backgroundColor: UIColors.red,
      textColor: Colors.white,
      textStyle: UITypography.selectedMenuLabel,
    ),
    cardTheme: CardTheme(
      color: UIColors.background,
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
    listTileTheme: const ListTileThemeData(
      dense: true,
      contentPadding: EdgeInsets.zero,
      iconColor: UIColors.primary,
      textColor: UIColors.paragraph,
      titleTextStyle: UITypography.h3,
      subtitleTextStyle: UITypography.listTileSubtitle,
    ),
    dividerTheme: const DividerThemeData(
      color: UIColors.border,
      thickness: 1,
      space: 1,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      elevation: 0,
      backgroundColor: UIColors.primary,
      foregroundColor: Colors.white,
      shape: CircleBorder(),
    ),
    expansionTileTheme: const ExpansionTileThemeData(
      iconColor: UIColors.primary,
      collapsedIconColor: UIColors.primary,
      textColor: UIColors.paragraph,
      collapsedTextColor: UIColors.paragraph,
      collapsedBackgroundColor: Colors.white,
      backgroundColor: UIColors.background,
      tilePadding: EdgeInsets.symmetric(
        horizontal: UISpacing.ll,
        vertical: UISpacing.l,
      ),
      childrenPadding: EdgeInsets.only(
        left: UISpacing.ll,
        right: UISpacing.ll,
        bottom: UISpacing.ll,
      ),
      shape: Border.symmetric(),
      collapsedShape: Border.symmetric(),
    ),
    dialogTheme: DialogTheme(
      backgroundColor: UIColors.menuBackground,
      titleTextStyle: UITypography.dialogTitle.copyWith(
        color: UIColors.paragraph,
      ),
      contentTextStyle: UITypography.dialogText.copyWith(
        color: UIColors.paragraph,
      ),
    ),
    tabBarTheme: const TabBarTheme(
      labelStyle: UITypography.button,
      unselectedLabelStyle: UITypography.button,
      labelColor: UIColors.primary,
      unselectedLabelColor: UIColors.paragraph,
      indicatorColor: UIColors.primary,
      dividerColor: UIColors.border,
      indicatorSize: TabBarIndicatorSize.tab,
      labelPadding: EdgeInsets.symmetric(
        horizontal: UISpacing.l,
        vertical: UISpacing.m,
      ),
    ),
    chipTheme: ChipThemeData(
      showCheckmark: false,
      backgroundColor: Colors.white,
      selectedColor: UIColors.background,
      labelStyle: UITypography.menuLabel.copyWith(color: UIColors.paragraph),
      side: WidgetStateBorderSide.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const BorderSide(color: UIColors.primary);
        }

        return const BorderSide(color: UIColors.border);
      }),
    ),
  );
}
