// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bulgarian (`bg`).
class AppLocalizationsBg extends AppLocalizations {
  AppLocalizationsBg([String locale = 'bg']) : super(locale);

  @override
  String get app_title => 'СБА';

  @override
  String get form_email => 'Имейл адрес';

  @override
  String get form_email_hint => 'Въведете вашият имейл...';

  @override
  String get form_password => 'Парола';

  @override
  String get form_old_password => 'Предишна парола';

  @override
  String get form_new_password => 'Нова парола';

  @override
  String get form_password_hint => 'Въведете вашата парола...';

  @override
  String get form_confirm_password => 'Повтори парола';

  @override
  String get form_confirm_password_hint => 'Повторете вашата парола...';

  @override
  String get form_company_name => 'Име на фирма';

  @override
  String get form_company_name_hint => 'Въведете името на фирмата...';

  @override
  String get form_eik => 'ЕИК';

  @override
  String get form_eik_hint => 'Въведете единен идентификационен код...';

  @override
  String get form_name => 'Име';

  @override
  String get form_name_hint => 'Въведете вашето име...';

  @override
  String get form_driver_name => 'Име на водача';

  @override
  String get form_driver_name_hint => 'Въведете име на водача...';

  @override
  String get form_last_name => 'Фамилия';

  @override
  String get form_last_name_hint => 'Въведете вашата фамилия...';

  @override
  String get form_city => 'Град';

  @override
  String get form_city_hint => 'Въведете града, в който живеете...';

  @override
  String get form_city_delivery_hint => 'Въведете град за доставка...';

  @override
  String get form_city_choose_hint => 'Изберете град...';

  @override
  String get form_office => 'Офис';

  @override
  String get form_office_speedy => 'Офис на Спиди';

  @override
  String get form_office_hint => 'Започнете да въвеждате ...';

  @override
  String get form_zip => 'Пощенски код';

  @override
  String get form_zip_hint => 'Въведете пощенски код...';

  @override
  String get form_address => 'Адрес';

  @override
  String get form_address_hint => 'Въведете вашия адрес...';

  @override
  String get form_complex => 'Кв. / ж.к.';

  @override
  String get form_complex_hint => 'Въведете квартал / жилищен комплекс...';

  @override
  String get form_street => 'Улица';

  @override
  String get form_street_hint => 'Въведете улица...';

  @override
  String get form_street_number => 'Номер';

  @override
  String get form_street_number_hint => '№';

  @override
  String get form_block => 'Блок';

  @override
  String get form_block_hint => 'Въведете блок...';

  @override
  String get form_entry => 'Вход';

  @override
  String get form_entry_hint => 'Вход';

  @override
  String get form_floor => 'Етаж';

  @override
  String get form_floor_hint => 'Етаж';

  @override
  String get form_apartment => 'Ап.';

  @override
  String get form_apartment_hint => 'Ап.';

  @override
  String get form_phone => 'Телефон';

  @override
  String get form_phone_hint => 'Въведете телефонен номер...';

  @override
  String get form_driver_phone => 'Телефон на водача';

  @override
  String get form_driver_phone_hint => 'Въведете телефон на водача...';

  @override
  String get form_brand => 'Марка';

  @override
  String get form_brand_hint => 'Изберете марка...';

  @override
  String get form_vehicle => 'Автомобил';

  @override
  String get form_vehicle_hint => 'Изберете автомобил...';

  @override
  String get form_model => 'Модел';

  @override
  String get form_model_hint => 'Изберете модел...';

  @override
  String get form_plate_number => 'Регистрационен номер';

  @override
  String get form_plate_number_hint => 'Въведете Регистрационен номер...';

  @override
  String get form_notification_hint => 'Съгласен съм да получавам следните съобщения по имейл';

  @override
  String get form_sms_hint => 'Съгласен съм да получавам следните СМС-и';

  @override
  String get form_type_hint => 'Този профил ще бъде използван от';

  @override
  String get form_image_hint => 'Добавяне на изображение';

  @override
  String get form_image_hint_gallery => 'Галерия';

  @override
  String get form_image_hint_camera => 'Камера';

  @override
  String get form_question => 'Задайте своя въпрос';

  @override
  String get form_question_hint => 'Въведете вашия въпрос...';

  @override
  String get form_zone => 'Зона';

  @override
  String get form_zone_hint => 'Изберете зона...';

  @override
  String get form_transmission => 'Скоростна кутия';

  @override
  String get form_transmission_hint => 'Изберете вид скоростна кутия...';

  @override
  String get form_drive => 'Задвижване';

  @override
  String get form_drive_hint => 'Изберете вид задвижване...';

  @override
  String get form_failure_type => 'Причина за обездвижване';

  @override
  String get form_failure_type_hint => 'Изберете причина за обездвижване...';

  @override
  String get form_info => 'Допълнителна информация';

  @override
  String get form_info_hint => 'Въведете допълнителна информация...';

  @override
  String get form_card_number => 'Номер на карта';

  @override
  String get form_card_number_hint => 'Въведете номер на карта...';

  @override
  String get form_barcode => 'Баркод';

  @override
  String get form_barcode_hint => 'Въведете баркод...';

  @override
  String get form_date => 'Дата';

  @override
  String get form_date_hint => 'Изберете дата...';

  @override
  String get form_start_date => 'Начална дата';

  @override
  String get form_start_date_hint => 'Изберете начална дата...';

  @override
  String get form_end_date => 'Крайна дата';

  @override
  String get form_end_date_hint => 'Изберете крайна дата...';

  @override
  String get form_valid_duration => 'Валидност в месеци';

  @override
  String get form_valid_duration_hint => 'Изберете валидност...';

  @override
  String get form_service => 'Сервиз';

  @override
  String get form_service_hint => 'Въведете сервиз...';

  @override
  String get form_odometer => 'Километраж';

  @override
  String get form_odometer_hint => 'Въведете километраж...';

  @override
  String get form_oil => 'Масло';

  @override
  String get form_oil_hint => 'Въведете име на масло...';

  @override
  String get form_quantity => 'Количество';

  @override
  String get form_quantity_hint => 'Въведете количество...';

  @override
  String get form_price => 'Цена';

  @override
  String get form_price_hint => 'Въведете цена...';

  @override
  String get form_note => 'Бележка';

  @override
  String get form_note_hint => 'Тук можете да запишете бележка...';

  @override
  String get form_vehicle_service => 'Сервизно обслужване';

  @override
  String get form_vehicle_service_hint => 'Въведете вид на сервизно обсужване...';

  @override
  String get form_vehicle_filter_hint => 'Сменени филтри';

  @override
  String get form_dealer => 'Търговец';

  @override
  String get form_dealer_hint => 'Въведете търговец...';

  @override
  String get form_station => 'Център';

  @override
  String get form_station_hint => 'Въведете център...';

  @override
  String get form_dealer_choose_hint => 'Изберете търговец...';

  @override
  String get form_fuel => 'Гориво';

  @override
  String get form_fuel_hint => 'Изберете гориво...';

  @override
  String get form_price_per_liter => 'Цена за литър';

  @override
  String get form_price_per_liter_hint => 'Въведете цената за литър...';

  @override
  String get form_tyre_type_hint => 'Вид гуми';

  @override
  String get form_dot => 'DOT';

  @override
  String get form_dot_hint => 'Въведете DOT...';

  @override
  String get form_tyre => 'Гуми';

  @override
  String get form_tyre_hint => 'Изберете гуми...';

  @override
  String get form_country_vehicle => 'Държава на регистрация на превозното средство';

  @override
  String get form_country_hint => 'Изберете държава...';

  @override
  String get form_lpg_hint => 'Автомобилът има ли газова уредба?';

  @override
  String get form_mot_lpg_hint => 'Узаконяване на газова уредба - АГУ?';

  @override
  String get form_time_diapason => 'Часови диапазон';

  @override
  String get form_time_diapason_hint => 'Изберете часови диапазон...';

  @override
  String get form_time => 'Час';

  @override
  String get form_time_hint => 'Изберете час...';

  @override
  String get form_delivery_type => 'Начин на доставка';

  @override
  String get form_payment_type => 'Начин на плащане';

  @override
  String get form_additional_package => 'Допълнителни пакети';

  @override
  String get form_tyre_width => 'Ширина';

  @override
  String get form_tyre_width_hint => 'Изберете ширина...';

  @override
  String get form_tyre_height => 'Височина';

  @override
  String get form_tyre_height_hint => 'Изберете височина...';

  @override
  String get form_tyre_diameter => 'Диаметър';

  @override
  String get form_tyre_diameter_hint => 'Изберете диаметър...';

  @override
  String get form_category => 'Категория';

  @override
  String get form_category_hint => 'Изберете категория';

  @override
  String get form_notification_option_info => 'информационни';

  @override
  String get form_notification_option_promo => 'промоционални';

  @override
  String get form_type_option_person => 'Физическо лице';

  @override
  String get form_type_option_company => 'Юридическо лице';

  @override
  String get form_type_option_vehicle_other => 'Друго МПС';

  @override
  String get form_type_option_oil_filter => 'Маслен филтър';

  @override
  String get form_type_option_air_filter => 'Въздушен филтър';

  @override
  String get form_type_option_fuel_filter => 'Горивен филтър';

  @override
  String get form_type_option_cabin_filter => 'Поленов филтър';

  @override
  String get form_type_option_winter_tire => 'Зимни гуми';

  @override
  String get form_type_option_summer_tire => 'Летни гуми';

  @override
  String get form_type_option_seasonal_tire => 'Всесезонни гуми';

  @override
  String get form_type_option_yes => 'Да';

  @override
  String get form_type_option_no => 'Не';

  @override
  String get form_type_option_car => 'Лек автомобил';

  @override
  String get form_type_option_trailer => 'Ремарке / каравана';

  @override
  String get form_type_option_delivery_address => 'Доставка до адрес';

  @override
  String get form_type_option_delivery_speedy => 'Доставка до офис на Спиди';

  @override
  String get form_type_option_payment_delivery => 'Плащане при доставка';

  @override
  String get form_type_option_payment_card => 'Плащане с карта';

  @override
  String get form_type_option_payment_bank => 'Банков превод';

  @override
  String get form_type_option_drive_type_fwd => 'Предно';

  @override
  String get form_type_option_drive_type_rwd => 'Задно';

  @override
  String get form_type_option_drive_type_awd => 'Пълно';

  @override
  String get form_type_option_drive_type_4wd => '4x4';

  @override
  String get form_type_option_transmission_type_manual => 'Ръчна';

  @override
  String get form_type_option_transmission_type_automatic => 'Автоматична';

  @override
  String get form_type_option_transmission_type_semiautomatic => 'Полуавтоматична';

  @override
  String get form_type_option_transmission_type_cvt => 'Вариаторна';

  @override
  String get form_validation_email => 'Невалиден имейл';

  @override
  String form_validation_min_length(Object length) {
    return 'Минимална дължина $length символа';
  }

  @override
  String get form_validation_zip => 'Невалиден код';

  @override
  String get form_validation_phone => 'Невалиден номер';

  @override
  String get form_validation_required => 'Задължително поле';

  @override
  String get form_validation_password_match => 'Паролите не съвпадат';

  @override
  String get form_validation_email_taken => 'Имейла е взет';

  @override
  String get form_validation_numeric => 'Невалидно число';

  @override
  String get form_validation_date => 'Невалидна дата';

  @override
  String get form_validation_license_plate => 'Невалиден регистрационен номер';

  @override
  String get navigation_home => 'Начало';

  @override
  String get navigation_road_assistance => 'Пътна помощ';

  @override
  String get navigation_road_assistance_request => 'Пътна помощ за мен';

  @override
  String get navigation_road_assistance_request_other => 'Пътна помощ за друг водач';

  @override
  String get navigation_mot => 'ГТП';

  @override
  String get navigation_mot_request => 'Заявка за ГТП';

  @override
  String get navigation_parking => 'Паркиране';

  @override
  String get navigation_parking_request => 'Паркиране в зона';

  @override
  String get navigation_insurance => 'Застраховка';

  @override
  String get navigation_other => 'Други';

  @override
  String get navigation_subscription => 'Клубна/членска карта';

  @override
  String get navigation_activate_subscription => 'Активиране на карта';

  @override
  String get navigation_buy_subscription => 'Закупуване на карта';

  @override
  String get navigation_subscription_details => 'Детайли по карта';

  @override
  String get navigation_legal_help => 'Правна помощ';

  @override
  String get navigation_training => 'Учебна дейност';

  @override
  String get navigation_toll => 'Винетен стикер';

  @override
  String get navigation_toll_check => 'Провери винетка';

  @override
  String get navigation_toll_buy => 'Купи винетка';

  @override
  String get navigation_service_book => 'Сервизна книжка';

  @override
  String get navigation_road_cameras => 'Камери СБА';

  @override
  String get navigation_notification => 'Известия';

  @override
  String get navigation_partners => 'Партньори';

  @override
  String get navigation_information => 'Информация';

  @override
  String get navigation_profile_edit => 'Редакция на профил';

  @override
  String get navigation_profile_password => 'Промяна на парола';

  @override
  String get navigation_vehicles => 'Моите автомобили';

  @override
  String get navigation_vehicles_new => 'Добавяне на автомобил';

  @override
  String get navigation_vehicles_edit => 'Промяна на автомобил';

  @override
  String get navigation_book_service => 'Сервизно обслужване';

  @override
  String get navigation_book_fuel => 'Гориво';

  @override
  String get navigation_book_engine_oil => 'Масло - двигател';

  @override
  String get navigation_book_transmission_oil => 'Масло - скоростна кутия';

  @override
  String get navigation_book_tyres => 'Гуми';

  @override
  String get navigation_book_other_service => 'Друго сервизно обслужване';

  @override
  String get navigation_book_new_tyres => 'Закупени гуми';

  @override
  String get navigation_book_swap_tyres => 'Сезонна смяна на гуми';

  @override
  String get navigation_book_mot => 'Годишни технически прегледи';

  @override
  String get empty_notification => 'Нямате известия за момента';

  @override
  String get empty_vehicles => 'Нямате автомобили в момента';

  @override
  String get empty_common => 'Нямате данни за момента';

  @override
  String get empty_search => 'Няма намерени резултати';

  @override
  String empty_subscription(Object vehicle) {
    return 'Нямате валидни абонаментни/членски карти за автомобил $vehicle';
  }

  @override
  String empty_insurance(Object vehicle) {
    return 'Нямате валидни застраховки за автомобил $vehicle';
  }

  @override
  String empty_toll(Object vehicle) {
    return 'Нямате валидна винетка за автомобил $vehicle';
  }

  @override
  String get empty_toll_without_vehicle => 'Няма валидна винетка';

  @override
  String empty_mot(Object vehicle) {
    return 'Нямате валиден годишен технически преглед за автомобил $vehicle';
  }

  @override
  String get empty_request => 'Няма заявки за момента';

  @override
  String get empty_newspaper => 'Няма ново издание в момента';

  @override
  String get empty_cards => 'Нямате карти за момента';

  @override
  String get action_add_vehicle => 'Добави автомобил';

  @override
  String get action_request_parking => 'Заявка за паркиране';

  @override
  String get action_send_request => 'Изпрати заяка';

  @override
  String get action_request => 'Заяви';

  @override
  String get action_prolong_parking => 'Удължаване на паркирането';

  @override
  String get action_choose_coordinates => 'Избери други координати';

  @override
  String get action_choose_position => 'Избери местоположение';

  @override
  String get action_buy_toll => 'Купи винетка';

  @override
  String get action_reserve_mot => 'Запиши час за ГТП';

  @override
  String get action_search => 'Търси';

  @override
  String get action_add => 'Добавяне';

  @override
  String get action_activate => 'Активиране';

  @override
  String get action_edit => 'Редактиране';

  @override
  String get action_delete => 'Изтриване';

  @override
  String get action_save => 'Запази';

  @override
  String get action_register => 'Регистрация';

  @override
  String get action_login => 'Вход';

  @override
  String get action_recover => 'Възстанови';

  @override
  String get action_exit => 'Изход';

  @override
  String get action_send => 'Изпрати';

  @override
  String get action_send_sms => 'Изпрати SMS';

  @override
  String get action_call => 'Обади се';

  @override
  String get action_navigate => 'Навигация';

  @override
  String get action_ok => 'Ok';

  @override
  String get action_cancel => 'Отказ';

  @override
  String get action_yes => 'Да';

  @override
  String get action_no => 'Не';

  @override
  String get action_check => 'Проверка';

  @override
  String get action_continue => 'Продължи';

  @override
  String get action_back => 'Назад';

  @override
  String get action_close => 'Затвори';

  @override
  String get action_confirm => 'Потвърди';

  @override
  String get action_pay => 'Плати';

  @override
  String get action_make_order => 'Направи поръчка';

  @override
  String get action_track => 'Проследи';

  @override
  String get message_profile_not_finished => 'Незавършен профил';

  @override
  String get message_success_create => 'Добавянето е успешно';

  @override
  String get message_success_delete => 'Изтриването е успешно';

  @override
  String get message_success_edit => 'Промените бяха записани успешно';

  @override
  String get message_success_request => 'Заявката е изпратена успешно';

  @override
  String get message_success_subscription => 'Заявката е изпратена успешно! Ще получите имейл инструкции за следващи стъпки.';

  @override
  String get message_feature_locked_title => 'Повечето функции са достъпни само за регистрирани потребители';

  @override
  String get message_feature_locked_text => 'За да използвате всички функции на приложението трябва да се впишете.';

  @override
  String get message_mot_expiring_title => 'Годишният ви технически преглед изтича';

  @override
  String message_mot_expiring_text(Object plate) {
    return 'ГТП за автомобил $plate изтича скоро.';
  }

  @override
  String get message_delete_title => 'Изтриване';

  @override
  String get message_cancel_title => 'Отказ';

  @override
  String get message_delete_text => 'Сигурни ли сте, че искате да изтриете записа';

  @override
  String get message_delete_request_text => 'Сигурни ли сте, че искате да откажете заявката';

  @override
  String get message_delete_vehicle_title => 'Изтриване на автомобил';

  @override
  String message_delete_vehicle_text(Object plate) {
    return 'Сигурни ли сте, че искате да изтриете от профила си атомобил с регистрационен номер $plate?';
  }

  @override
  String get error_title => 'Грешка';

  @override
  String get error_general_text => 'Възникна неочаквана грешка. Опитайте пак.';

  @override
  String get error_location_text => 'Неуспешно определяне на местоположение. Опитайте пак.';

  @override
  String get error_payment_text => 'Възникна грешка при плащането. Опитайте пак.';

  @override
  String get error_sms => 'Изпращането на СМС не беше успешно. Опитайте пак';

  @override
  String get error_call => 'Осъществяването на разговор не беше успешно. Опитайте пак';

  @override
  String get error_network_text => 'Няма връзка с сървъра. Опитайте по късно.';

  @override
  String get error_email_not_found => 'Невалиден имейл адрес или профила ви не е активиран! Изпращане на активационен имейл?';

  @override
  String get error_wrong_username => 'Грешно потребителско име или парола';

  @override
  String get error_activation => 'Профила не е активиран! Изпращане на активационен имейл?';

  @override
  String get error_toll_start_date => 'Невалидна начална дата! Променете датата и опитайте пак.';

  @override
  String get error_toll_email => 'Невалиден имейл! проверете имейл адреса и опитайте пак.';

  @override
  String get error_toll_vehicle => 'Невалиден регистрационен номер! Проверете въведения регистрационен номер и опитайте пак.';

  @override
  String get error_toll_activated => 'Винетката еече активирана.';

  @override
  String get error_toll_has_prucase => 'Превозното средство име действаща винетка за този период.';

  @override
  String get login_title => 'Добре дошли отново!';

  @override
  String get login_subtitle => 'ГТП, пътна помощ, застраховки и още, всичко на едно място!';

  @override
  String get login_remember_me => 'Запомни ме';

  @override
  String get login_forgotten_password => 'Забравена парола?';

  @override
  String get login_guest_sign_in => 'Вход без регистрация';

  @override
  String get login_register_hint => 'Нямате профил?';

  @override
  String get login_send_message_success_title => 'Активационен имейл';

  @override
  String get login_send_message_success_text => 'Изпратихме инструкции на имейл адреса.';

  @override
  String get register_title => 'Добре дошли!';

  @override
  String get register_subtitle => 'ГТП, пътна помощ, застраховки и още, всичко на едно място!';

  @override
  String get register_gdpr => 'Давам съгласие за обработка на лични данни';

  @override
  String get register_sba => 'Съгласен съм да свържа профила си с базата данни на СБА и да получавам известия';

  @override
  String get register_sign_in_hint => 'Вече имате регистрация?';

  @override
  String get register_message_success_title => 'Успешна регистрация';

  @override
  String get register_message_success_text => 'Изпратихме инструкции на имейл адреса с който се регистрирахте';

  @override
  String get recovery_title => 'Възстановяване на парола';

  @override
  String get recovery_subtitle => 'Ще изпратим инструкции на имейл-а с който сте регистриран.';

  @override
  String get recovery_message_success_text => 'Изпратихме инструкции на имейл адреса с който сте регистриран';

  @override
  String home_title(Object name) {
    return 'Здравейте, $name!';
  }

  @override
  String get home_title_guest => 'Гост';

  @override
  String get home_section_subscription => 'Абонаментни/членски карти';

  @override
  String get home_section_insurance => 'Валидни застраховки';

  @override
  String get home_section_toll => 'Валидна винетка';

  @override
  String get home_section_mot => 'Валиден годишен технически преглед';

  @override
  String get home_section_other => 'Други';

  @override
  String get home_section_newspaper => 'Вестник';

  @override
  String get home_vehicle_tile_subscription => 'Член на СБА:';

  @override
  String get home_vehicle_tile_mot => 'ГТП:';

  @override
  String get home_vehicle_tile_road_insurance => 'Активна застраховка ГО:';

  @override
  String get home_vehicle_tile_insurance => 'Активна застраховка Каско:';

  @override
  String home_insurance_tile_company(Object company) {
    return 'Застраховател: $company';
  }

  @override
  String home_insurance_tile_valid(Object date) {
    return 'Период на покритие: $date';
  }

  @override
  String home_toll_tile_title(Object data) {
    return 'Винетка от $data';
  }

  @override
  String home_toll_tile_valid(Object date) {
    return 'Срок на валидност: до $date';
  }

  @override
  String home_mot_tile_title(Object date) {
    return 'ГТП от $date';
  }

  @override
  String home_mot_tile_valid(Object date) {
    return 'Срок на валидност: $date';
  }

  @override
  String get home_newspaper_tile => 'Вестник';

  @override
  String get home_newspaper_tile_year_label => 'година';

  @override
  String get home_newspaper_tile_issue_label => 'Брой:';

  @override
  String get profile_navigation_edit_title => 'Моят профил';

  @override
  String get profile_navigation_edit_subtitle => 'Редактиране на лични данни';

  @override
  String get profile_navigation_password_title => 'Сигурност';

  @override
  String get profile_navigation_password_subtitle => 'Промяна на парола';

  @override
  String get profile_navigation_cars_title => 'Моите автомобили';

  @override
  String get profile_navigation_cars_subtitle => 'Редактиране на моите автомобили';

  @override
  String get profile_navigation_notifications_title => 'Известия';

  @override
  String get profile_navigation_notifications_subtitle => 'Разрешения за известия';

  @override
  String get profile_edit_personal_data_section => 'Лични данни';

  @override
  String get profile_edit_password_section => 'Парола';

  @override
  String get profile_edit_address_section => 'Адрес';

  @override
  String get profile_notification_email_section => 'Имейл съобщения';

  @override
  String get profile_notification_sms_section => 'СМС-и';

  @override
  String get vehicle_edit_information_section => 'Информация за автомобила';

  @override
  String get vehicle_edit_place_section => 'Местодомуване на автомобила';

  @override
  String get training_map_subtitle => 'Учебни центрове';

  @override
  String get training_description_subtitle => 'Повече за дейността';

  @override
  String get training_description_text => 'Съюзът на българските автомобилисти обучава водачи на моторни превозни средства от 1960 г. СБА съхрани традициите и доброто си име в организирането и провеждането на дейността по обучение на водачи от категория “В”. Сега организацията разполага с над 23 учебни центъра с учебни автомобили в цялата страна.\nКурсовете в СБА се водят съобразно изискванията на Наредба №37 и учебната документация за съответната категория.';

  @override
  String get training_item_1_title => 'КАТЕГОРИЯ „B“ – ВОДАЧИ НА МПС ДО 8 МЕСТА И 3.5 ТОНА';

  @override
  String get training_item_1_text => 'Изисквания:\n- изпитът по Теория може да се проведе в рамките на месеца преди навършване на 18 години;\n\n- лична карта;\n- декларация за данните от дипломата си;\n- снимка паспортен формат – 1 бр.;\n- диплома за завършено образование;\n\nОБУЧЕНИЕ ПО ТЕОРИЯ:\n\nОбучението по теория се извършва съгласно Наредба №37 за Обучение на водачи. Обучението се провежда в удобно за курсиста време. След завършване на теоретичното обучение се полага вътрешен изпит по теория, след което следва изпит пред ИААА:\n\n– решава се 1 бр. листовка (тест) общо 45 въпроса. В случай, че резултатът е отрицателен следва ново явяване на изпит.\n\nОБУЧЕНИЕ ПО ПРАКТИКА:\n\nЧасовете по кормуване са общо 31 на брой с времетраене от 50 минути всеки.\n\nСлед завършване на практическото обучение се провежда вътрешен изпит по кормуване и следва изпит пред ИААА.\n\nПрактическият изпит се провежда при успешно положен изпит по теория.\n\nЦЕНА НА КУРСА: ТЯ Е РАЗЛИЧНА В ЗАВИСИМОСТ ОТ ВИДА НА УЧЕБНИЯ АВТОМОБИЛ, УЧЕБНИЯ КАБИНЕТ И ДР.\n\nПосочената цена включва теоретичното и практическото обучение, както и всички вътрешни изпити до получаване на свидетелство за завършен курс.\nДопълнително се заплаща явяването на практически и теоретичен изпит пред ДАИ.\n\nИнформация за допълнителното заплащане за изпитите пред ДАИ може да получите от съответния офис на СБА при записване за курс.\n\nНЕ СЕ СЪБИРАТ ДОПЪЛНИТЕЛНИ СРЕДСТВА ЗА ТЕОРЕТИЧНОТО И ПРАКТИЧЕСКО ОБУЧЕНИЕ В РАМКИТЕ НА КУРСА И ВЪТРЕШНИТЕ ИЗПИТИ.';

  @override
  String get training_item_2_title => 'ВЪЗСТАНОВЯВАНЕ НА КОНТРОЛНИ ТОЧКИ:';

  @override
  String get training_item_2_text => 'Водачите на моторни превозно средства притежават 39 контролни точки.\n\nПри определени нарушения на Закона за движение по пътищата контролните органи отнемат от точките на водачите. Ежегодно водачите могат след допълнително обучение в лицензирани кабинети на СБА да възстановяват 13 точки. Обучението е теоретично в един ден и завършва с решаването на листовка. За да участвате в това обучение, трябва да не сте изгубили всички контролни точки, както и да сте заплатили всички дължими глоби към КАТ – Пътна полиция.\nЗа курса на обучение се изисква да представите лична карта, свидетелство за правоуправление, синия контролен талон към свидетелството, или ако е отнет, акта, с който е отнет талона.';

  @override
  String get legal_help_tab_cases => 'Казуси';

  @override
  String get legal_help_tab_questions => 'Казуси извън посочените';

  @override
  String get legal_help_send_question_section => 'Изпращане на въпрос';

  @override
  String get legal_help_call_for_help_section => 'Свържете се с кол център';

  @override
  String get legal_help_send_message_success_title => 'Успех';

  @override
  String get legal_help_send_message_success_text => 'Успешно изпратихте въпроса си! Очаквайте отговор по имейл.';

  @override
  String get parking_tile_city => 'Град:';

  @override
  String get parking_tile_vehicle => 'Автомобил:';

  @override
  String get parking_tile_zone => 'Зона:';

  @override
  String get parking_tile_until => 'Платен до:';

  @override
  String get parking_sms_message_title => 'Потвърждение';

  @override
  String get parking_sms_message_test => 'Изпратихте ли СМС за паркиране?';

  @override
  String get road_assistance_services_section => 'Услуги';

  @override
  String get road_assistance_requests_section => 'Вашите заявки';

  @override
  String get road_assistance_vehicle_section => 'Данни за автомобила';

  @override
  String get road_assistance_driver_section => 'Лични данни';

  @override
  String road_assistance_tile_request_number(Object request) {
    return 'Заявка № $request';
  }

  @override
  String get road_assistance_tile_reason => 'Причина:';

  @override
  String get road_assistance_tile_driver => 'Водач:';

  @override
  String get road_assistance_tile_vehicle => 'Автомобил:';

  @override
  String get road_assistance_tile_submitDate => 'Заявка от:';

  @override
  String get road_assistance_tile_arrivalDate => 'Очакван час:';

  @override
  String get road_assistance_tile_status => 'Статус:';

  @override
  String get road_assistance_status_requested => 'В обработка';

  @override
  String get road_assistance_status_progress => 'В изпълнение';

  @override
  String get road_assistance_status_completed => 'Завършена';

  @override
  String get road_assistance_status_cancelled => 'Отказана';

  @override
  String get road_assistance_request_other_caption => '* Служител от нашия кол център ще се свърже с водача за установяване на неговата локация';

  @override
  String get road_assistance_cancel_message_title => 'Отмяна на заявка за пътна помощ';

  @override
  String road_assistance_cancel_message_text(Object plate) {
    return 'Сигурни ли сте, че искате да отмените пристигането на пътна помощ за автомобил $plate?';
  }

  @override
  String get subscription_details_category_label => 'Тип на картата:';

  @override
  String get subscription_details_type_label => 'Вид на картата:';

  @override
  String get subscription_details_vehicle_label => 'Автомобил:';

  @override
  String get subscription_details_card_number_label => 'Номер на картата:';

  @override
  String get subscription_details_valid_from_label => 'Валидна от:';

  @override
  String get subscription_details_valid_until_label => 'Валидна до:';

  @override
  String get subscription_activate_activate_section => 'Тук може да активирате членска карта на СБА, закупена от обектите на нашите партньори от ОМВ.';

  @override
  String get subscription_activate_information_section => 'Данни за автомобила';

  @override
  String get subscription_activate_place_section => 'Местодомуване на автомобила';

  @override
  String get subscription_activate_gdpr_hint => 'Запознах се с политиката за защита на личните данни и уведомлението за поверително третиране на СБА';

  @override
  String get subscription_activate_omv_hint => 'Запознах се с условията, при които мога да използвам отстъпките в OMV';

  @override
  String get subscription_activate_terms_hint => 'Запознах се и приемам общите условията по членство в СБА';

  @override
  String get subscription_activate_conditions_hint => 'Запознах се и приемам устава на СБА';

  @override
  String get subscription_tab_member_section => 'Членските карти са предназначени за физически лица. Ако сте фирма, вижте абонаментните ни карти.';

  @override
  String get subscription_tab_subscription_section => 'Абонаментните карти са предназначени както за физически, така и за юридически лица.';

  @override
  String get subscription_tab_cards => 'Карти';

  @override
  String get subscription_tab_requests => 'Заявки';

  @override
  String get subscription_type_member => 'Членство';

  @override
  String get subscription_type_subscription => 'Абонамент';

  @override
  String get subscription_type_silver => 'Сребърна';

  @override
  String get subscription_type_silver_plus => 'Сребърна +';

  @override
  String get subscription_type_gold => 'Златна';

  @override
  String get subscription_type_platinum => 'Платинена';

  @override
  String subscription_order_title(Object type) {
    return 'Заявка за $type';
  }

  @override
  String get subscription_request_category_label => 'Тип на картата:';

  @override
  String get subscription_request_type_label => 'Вид на картата:';

  @override
  String get subscription_request_vehicle_label => 'Автомобил:';

  @override
  String get subscription_request_status_label => 'Статус:';

  @override
  String get subscription_request_delivery_label => 'Доставка:';

  @override
  String get subscription_request_status_requested => 'В обработка';

  @override
  String get subscription_request_status_completed => 'Завършена';

  @override
  String get service_book_oil_label => 'Масло:';

  @override
  String get service_book_autoservice_label => 'Сервиз:';

  @override
  String get service_book_odometer_label => 'Километраж:';

  @override
  String get service_book_quantity_label => 'Количество:';

  @override
  String get service_book_price_label => 'Цена:';

  @override
  String get service_book_changed_label => 'Сменени:';

  @override
  String get service_book_dealer_label => 'Търговец:';

  @override
  String get service_book_fuel_label => 'Гориво: ';

  @override
  String get service_book_value_label => 'Стойност:';

  @override
  String get service_book_brand_label => 'Марка:';

  @override
  String get service_book_model_label => 'Модел:';

  @override
  String get service_book_dot_label => 'DOT:';

  @override
  String get service_book_buy_date_label => 'Дата на закупуване:';

  @override
  String get service_book_date_label => 'Дата:';

  @override
  String get service_book_changed_by_label => 'Монтирани от:';

  @override
  String get service_book_station_label => 'ГТП пункт:';

  @override
  String get service_book_valid_label => 'Важи до:';

  @override
  String get service_book_size_label => 'Размер:';

  @override
  String service_book_months(Object duration) {
    return '$duration месеца';
  }

  @override
  String get toll_id_label => 'Идентификационен номер на винетка';

  @override
  String get toll_vehicle_type_label => 'Клас на превозното средство';

  @override
  String get toll_start_date_label => 'Начална дата и час на валидност';

  @override
  String get toll_valid_date_label => 'Крайна дата и час на валидност';

  @override
  String get toll_amount_label => 'Сума';

  @override
  String get toll_status_label => 'Статус';

  @override
  String get toll_status_active => 'Активна';

  @override
  String get toll_status_inactive => 'Не активна';

  @override
  String get toll_type_label => 'Вид винетка';

  @override
  String get toll_payment_type_label => 'Начин на плащане';

  @override
  String get toll_period_subtitle => 'Период на валидност';

  @override
  String get toll_period_hint => 'Електронната винетка е валидна за Уикенд от избраната дата на валидност. Можете да закупите винетка 30 дни преди началната дата на валидност.';

  @override
  String get toll_vehicle_data_subtitle => 'Данни за превозното средство';

  @override
  String get toll_email_subtitle => 'Имейл';

  @override
  String get toll_email_hint => 'Въведете имейл адрес, на който да получите електронната си винетка.';

  @override
  String get toll_confirm_message_title => 'Потвърждение на данните';

  @override
  String get toll_success_message_title => 'Вие успешно закупихте винeтка';

  @override
  String toll_success_message_text(Object email) {
    return 'На имейл адрес $email ще получите потвърждение за успешна покупка и разписка за активна е-винетка.';
  }

  @override
  String toll_info_message_title(Object plate) {
    return 'Вие успешно заявихте винетка за автомобил $plate';
  }

  @override
  String get toll_info_message_text => 'За да завършите закупуването на винетка, моля изберете бутона по-долу и преминете към плащане.';

  @override
  String get toll_message_plate_number_label => 'Рег номер:';

  @override
  String get toll_message_country_label => 'Държава:';

  @override
  String get toll_message_type_label => 'Тип:';

  @override
  String get toll_message_valid_label => 'Валидна от:';

  @override
  String get toll_message_valid_to_label => 'Валидна до:';

  @override
  String get toll_message_issued_label => 'Издадена на:';

  @override
  String get toll_message_validity_label => 'Продължителност:';

  @override
  String get toll_message_price_label => 'Сума за плащане:';

  @override
  String get toll_message_paid_label => 'Заплатена сума:';

  @override
  String get toll_message_email_label => 'Имейл:';

  @override
  String get mot_status_active => 'Активна';

  @override
  String get mot_status_rejected => 'Отхвърлена';

  @override
  String get mot_status_cancelled => 'Отказана';

  @override
  String get mot_status_completed => 'Завършена';

  @override
  String get mot_status_active_many => 'Активни';

  @override
  String get mot_status_rejected_many => 'Отхвърлени';

  @override
  String get mot_status_cancelled_many => 'Отказани';

  @override
  String get mot_status_completed_many => 'Завършени';

  @override
  String mot_tile_id_label(Object id) {
    return 'Заявка № $id';
  }

  @override
  String get mot_tile_vehicle_label => 'Автомобил:';

  @override
  String get mot_tile_place_label => 'Пункт:';

  @override
  String get mot_tile_date_label => 'Дата:';

  @override
  String get mot_tile_time_label => 'Час:';

  @override
  String get mot_tile_services_label => 'Услуги:';

  @override
  String get mot_tile_status => 'Статус:';

  @override
  String get mot_request_valid_caption => 'Моля, имайте предвид, че трябва да пристигнете в техническия център 10 минути по-рано от запазения от Вас час за годишен преглед! При записани няколко часа за ГТП за един и същи автомобил, клиентът губи всички часове!';

  @override
  String get mot_documents_button => 'Необходими документи за ГТП';

  @override
  String get mot_vehicle_category_car => 'Лек автомобил';

  @override
  String get mot_vehicle_category_truck_under_35 => 'Камион до 3.5т';

  @override
  String get mot_vehicle_category_truck_over_35 => 'Камион над 3.5т';

  @override
  String get mot_vehicle_category_bus => 'Автобус';

  @override
  String get mot_service_type_inspection => 'ГТП';

  @override
  String get mot_service_type_lpg => 'Узаконяване на АГУ';

  @override
  String get partners_filter_all => 'Всички';

  @override
  String get partners_details_info_section => 'Информация:';
}
