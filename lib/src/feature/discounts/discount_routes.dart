import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/discounts/details/discount_details_screen.dart';
import 'package:sba/src/feature/discounts/view/discounts_screen.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';

class DiscountRoute extends GoRouteData {
  const DiscountRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const DiscountsScreen();
  }
}

class DiscountDetailsRoute extends GoRouteData {
  const DiscountDetailsRoute({required this.$extra});

  final DiscountItem $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DiscountDetailsScreen(args: DiscountDetailsScreenArgs(item: $extra));
  }
}
