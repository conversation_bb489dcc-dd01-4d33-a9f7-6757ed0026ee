import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/auth/login/login_screen.dart';
import 'package:sba/src/feature/auth/recovery/recovery_screen.dart';
import 'package:sba/src/feature/auth/register/register_screen.dart';

part 'auth_router.g.dart';

@TypedGoRoute<SignInRoute>(
  path: '/auth',
  routes: [
    TypedGoRoute<RecoveryRoute>(path: 'recovery'),
    TypedGoRoute<RegisterRoute>(path: 'register'),
  ],
)
class SignInRoute extends GoRouteData {
  const SignInRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const LoginScreen();
}

class RecoveryRoute extends GoRouteData {
  const RecoveryRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const RecoveryScreen();
}

class RegisterRoute extends GoRouteData {
  const RegisterRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const RegisterScreen();
}
