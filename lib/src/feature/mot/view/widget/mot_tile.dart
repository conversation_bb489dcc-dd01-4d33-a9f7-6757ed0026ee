import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/avi_booking_data.dart';
import 'package:sba/src/repository/avi/model/types/avi_service_type.dart';
import 'package:sba/src/repository/avi/model/types/avi_status.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class MotTile extends StatelessWidget {
  const MotTile({
    required this.data,
    required this.onDelete,
    required this.onNavigate,
    super.key,
  });

  final AviBookingData data;
  final VoidCallback onDelete;
  final VoidCallback onNavigate;

  @override
  Widget build(BuildContext context) {
    final isActive = data.state == AviStatus.active;

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: UISpacing.m,
        horizontal: UISpacing.l,
      ),
      decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            spacing: UISpacing.m,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: Assets.icons.warehouse.svg(
                  colorFilter: ColorFilter.mode(
                    context.theme.colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      context.l10n.mot_tile_id_label(data.id),
                      style: context.textTheme.headlineMedium,
                      maxLines: 1,
                    ),
                    const Gap(UISpacing.m),
                    LabelWithText(
                      label: context.l10n.mot_tile_vehicle_label,
                      text: data.plateNumber,
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.mot_tile_place_label,
                      text: data.locationDetails.name ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.mot_tile_date_label,
                      text: data.date.formatDate(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.mot_tile_time_label,
                      text: data.slot,
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.mot_tile_services_label,
                      maxLines: 2,
                      text: data.services
                          .map((e) => e.localizedName(context))
                          .join(', '),
                    ),
                    const Gap(UISpacing.s),
                    if (data.state != null)
                      LabelWithText(
                        label: context.l10n.mot_tile_status,
                        text: data.state!.formattedValue(context),
                        textColor: data.state!.color,
                      ),
                  ],
                ),
              ),
              Visibility(
                visible: isActive,
                child: IconButton(
                  onPressed: onDelete,
                  icon: Assets.icons.delete.svg(),
                ),
              ),
            ],
          ),
          if (isActive) const Gap(UISpacing.ll),
          if (isActive)
            FilledButton(
              onPressed: onNavigate,
              child: Text(context.l10n.action_navigate),
            ),
        ],
      ),
    );
  }
}
