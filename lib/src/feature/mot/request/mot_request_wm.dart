import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/mot/request/mot_request_model.dart';
import 'package:sba/src/feature/mot/request/mot_request_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/repository/avi/model/book_avi_data.dart';
import 'package:sba/src/repository/avi/model/search_avi_data.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';
import 'package:toastification/toastification.dart';

typedef BookingRecord = ({
  AviPointSlotData slot,
  String plateNumber,
});

abstract interface class IMotRequestWidgetModel implements IWidgetModel {
  void checkMotCenter(SearchAviData searchData);

  void selectCenter(AviPointData data);

  void book(BookingRecord record);

  void onDocumentTap();

  StateNotifier<List<Place>> get places;

  StateNotifier<List<VehicleData>> get vehicles;

  StateNotifier<List<MarkerData<AviPointData>>> get foundCenters;

  StateNotifier<AviPointData?> get selectedCenter;
}

MotRequestWidgetModel defaultMotRequestWidgetModelFactory(
  BuildContext context,
) {
  return MotRequestWidgetModel(
    MotRequestModel(
      aviRepository: get(),
      vehicleRepository: get(),
      generalRepository: get(),
      errorHandler: get(),
    ),
  );
}

class MotRequestWidgetModel extends WidgetModel<MotRequestScreen, MotRequestModel> implements IMotRequestWidgetModel {
  MotRequestWidgetModel(super.model);

  final _places = StateNotifier<List<Place>>();
  final _vehicles = StateNotifier<List<VehicleData>>();
  final _foundCenters = StateNotifier<List<MarkerData<AviPointData>>>();
  final _selectedCenter = StateNotifier<AviPointData?>();
  final _searchData = StateNotifier<SearchAviData?>();

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    _places.accept(await model.places);
    _vehicles.accept(await model.vehicles);
  }

  @override
  void checkMotCenter(SearchAviData data) async {
    await context.showLoadingDialog();
    final result = await model.findAVIPoint(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _foundCenters.accept(
      result.maybeValue?.map((e) => MarkerData(coordinates: e.coordinates, data: e)).toList(),
    );

    _searchData.accept(data);
    _selectedCenter.accept(null);
  }

  @override
  void selectCenter(AviPointData data) {
    _selectedCenter.accept(data);
  }

  @override
  void book(BookingRecord record) async {
    if (_searchData.value == null) return;

    final data = BookAviData(
      searchAviData: _searchData.value!,
      selectedSlotData: record.slot,
      plateNumber: record.plateNumber,
    );

    await context.showLoadingDialog();
    final result = await model.book(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
    }

    context.showToast(
      type: ToastificationType.success,
      title: context.l10n.message_success_request,
    );

    context.pop();
  }

  @override
  StateNotifier<List<MarkerData<AviPointData>>> get foundCenters => _foundCenters;

  @override
  StateNotifier<AviPointData?> get selectedCenter => _selectedCenter;

  @override
  StateNotifier<List<Place>> get places => _places;

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  void onDocumentTap() {
    // TODO: implement onDocumentTap
  }
}
