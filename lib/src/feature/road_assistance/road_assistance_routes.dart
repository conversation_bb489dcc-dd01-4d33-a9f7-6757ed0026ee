import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/road_assistance/request/request_road_assistance_screen.dart';
import 'package:sba/src/feature/road_assistance/request_other/request_road_assistance_for_other_screen.dart';
import 'package:sba/src/feature/road_assistance/view/road_assistance_screen.dart';

class RoadAssistanceRoute extends GoRouteData {
  const RoadAssistanceRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RoadAssistanceScreen();
  }
}

class RequestRoadAssistanceRoute extends GoRouteData {
  const RequestRoadAssistanceRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RequestRoadAssistanceScreen();
  }
}

class RequestRoadAssistanceOtherRoute extends GoRouteData {
  const RequestRoadAssistanceOtherRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RequestRoadAssistanceForOtherScreen();
  }
}
