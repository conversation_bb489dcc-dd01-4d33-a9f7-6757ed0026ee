import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_model.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_screen.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';

abstract interface class ISubscriptionDetailsWidgetModel
    implements IWidgetModel {
  EntityStateNotifier<SubscriptionData> get subscription;
}

SubscriptionDetailsWidgetModel defaultSubscriptionDetailsWidgetModelFactory(
  BuildContext context,
) {
  return SubscriptionDetailsWidgetModel(
    SubscriptionDetailsModel(errorHandler: get(), repository: get()),
  );
}

class SubscriptionDetailsWidgetModel
    extends WidgetModel<SubscriptionDetailsScreen, SubscriptionDetailsModel>
    implements ISubscriptionDetailsWidgetModel {
  SubscriptionDetailsWidgetModel(super.model);

  final _subscription = EntityStateNotifier<SubscriptionData>();

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  Future<void> _loadData() async {
    _subscription.loading();
    final result = await model.getSubscription(widget.args.subscriptionId);
    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      return;
    }

    final data = result.maybeValue;

    data != null ? _subscription.content(data) : _subscription.error();
  }

  @override
  EntityStateNotifier<SubscriptionData> get subscription => _subscription;
}
