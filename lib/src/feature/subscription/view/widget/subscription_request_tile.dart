import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_delivery_type.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_order_status.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SubscriptionRequestTile extends StatelessWidget {
  const SubscriptionRequestTile({required this.data, super.key});

  final SubscriptionRequestData data;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: UISpacing.m,
        horizontal: UISpacing.l,
      ),
      decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            spacing: UISpacing.m,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: Assets.icons.featuredPlayList.svg(
                  colorFilter: ColorFilter.mode(
                    context.theme.colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      context.l10n.subscription_order_title(
                        data.subscriptionData?.category?.localizedName(
                              context,
                            ) ??
                            '',
                      ),
                      style: context.textTheme.headlineMedium,
                      maxLines: 1,
                    ),
                    const Gap(UISpacing.m),
                    LabelWithText(
                      label: context.l10n.subscription_request_category_label,
                      text: data.subscriptionData?.category?.localizedName(
                            context,
                          ) ??
                          '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.subscription_request_type_label,
                      text: data.subscriptionData?.type?.localizedName(
                            context,
                          ) ??
                          '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.subscription_request_vehicle_label,
                      text: data.vehicle?.plateNumber ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.subscription_request_delivery_label,
                      text: data.deliveryType.localizedName(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.subscription_request_status_label,
                      text: data.status?.localizedName(context) ?? '',
                      textColor: data.status?.color,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
