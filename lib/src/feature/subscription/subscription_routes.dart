import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_screen.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_screen.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_screen.dart';
import 'package:sba/src/feature/subscription/view/subscription_screen.dart';

class SubscriptionRoute extends GoRouteData {
  const SubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SubscriptionScreen();
  }
}

class ActivateSubscriptionRoute extends GoRouteData {
  const ActivateSubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ActivateSubscriptionScreen();
  }
}

class BuySubscriptionRoute extends GoRouteData {
  const BuySubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const BuySubscriptionScreen();
  }
}

class SubscriptionDetailsRoute extends GoRouteData {
  const SubscriptionDetailsRoute({required this.subscriptionId});

  final int subscriptionId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SubscriptionDetailsScreen(
      args: SubscriptionDetailsScreenArgs(
        subscriptionId: subscriptionId,
      ),
    );
  }
}
