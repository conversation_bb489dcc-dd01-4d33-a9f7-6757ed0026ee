import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class BuySubscriptionFormModel extends ElementaryModel {
  BuySubscriptionFormModel({
    required SubscriptionRepository subscriptionRepository,
    required UserRepository userRepository,
    required GeneralRepository generalRepository,
    required VehicleRepository vehicleRepository,
    super.errorHandler,
  })  : _subscriptionRepository = subscriptionRepository,
        _userRepository = userRepository,
        _generalRepository = generalRepository,
        _vehicleRepository = vehicleRepository;

  final SubscriptionRepository _subscriptionRepository;
  final UserRepository _userRepository;
  final VehicleRepository _vehicleRepository;
  final GeneralRepository _generalRepository;

  Future<List<Place>?> get places =>
      _generalRepository.getPlaces().then((e) => e.maybeValue);

  Future<UserData?> get user => _userRepository.getUser();

  Future<List<VehicleData>?> get vehicles => _vehicleRepository.getVehicles();

  Future<Result<void>> createRequest(SubscriptionRequestData data) =>
      _subscriptionRepository.createOrderRequest(data);
}
