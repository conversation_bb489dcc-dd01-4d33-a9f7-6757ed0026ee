import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_wm.dart';
import 'package:sba/src/feature/subscription/buy/widget/subscription_items_tab.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class BuySubscriptionScreen
    extends ElementaryWidget<IBuySubscriptionWidgetModel> {
  const BuySubscriptionScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultBuySubscriptionWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IBuySubscriptionWidgetModel wm) {
    return DefaultTabController(
      length: SubscriptionCategory.values.length,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            SliverTitle(
              text: context.l10n.navigation_buy_subscription,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
            const SliverGap(UISpacing.m),
            PinnedHeaderSliver(
              child: Container(
                padding: UISpacing.defaultElementHorizontalPadding,
                color: context.theme.colorScheme.surface,
                child: TabBar(
                  tabs: SubscriptionCategory.values
                      .map(
                        (it) => Text(
                          it.localizedName(context),
                          textAlign: TextAlign.center,
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
          ],
          body: TabBarView(
            children: [
              StateNotifierBuilder(
                listenableState: wm.memberships,
                builder: (context, data) => SubscriptionItemsTab(
                  key: PageStorageKey(SubscriptionCategory.membership.name),
                  hint: context.l10n.subscription_tab_member_section,
                  data: data,
                  onSelect: wm.onSubscriptionTap,
                ),
              ),
              StateNotifierBuilder(
                listenableState: wm.subscriptions,
                builder: (context, data) => SubscriptionItemsTab(
                  key: PageStorageKey(
                    SubscriptionCategory.subscription.name,
                  ),
                  hint: context.l10n.subscription_tab_subscription_section,
                  data: data,
                  onSelect: wm.onSubscriptionTap,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
