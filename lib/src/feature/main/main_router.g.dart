// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_router.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $mainRoute,
    ];

RouteBase get $mainRoute => ShellRouteData.$route(
      navigatorKey: MainRoute.$navigatorKey,
      factory: $MainRouteExtension._fromState,
      routes: [
        GoRouteData.$route(
          path: '/home',
          factory: $HomeRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'profile',
              factory: $ProfileRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: $EditProfileRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'password',
                  factory: $EditPasswordRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'notification_settings',
                  factory: $NotificationSettingsRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'vehicles',
                  factory: $MyVehiclesRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'new',
                      factory: $NewVehicleRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'edit',
                      factory: $EditVehicleRouteExtension._fromState,
                    ),
                  ],
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'notification',
              factory: $NotificationRouteExtension._fromState,
            ),
            GoRouteData.$route(
              path: 'subscription_details',
              factory: $SubscriptionDetailsRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/parking',
          factory: $ParkingRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'request',
              factory: $RequestParkingRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/road_assistance',
          factory: $RoadAssistanceRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'request',
              factory: $RequestRoadAssistanceRouteExtension._fromState,
            ),
            GoRouteData.$route(
              path: 'request_other',
              factory: $RequestRoadAssistanceOtherRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/toll',
          factory: $TollRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'check',
              factory: $CheckTollRouteExtension._fromState,
            ),
            GoRouteData.$route(
              path: 'buy',
              factory: $BuyTollRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/mot',
          factory: $MotRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'request',
              factory: $MotRequestRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/other',
          factory: $OtherRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/subscription',
          factory: $SubscriptionRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'activate',
              factory: $ActivateSubscriptionRouteExtension._fromState,
            ),
            GoRouteData.$route(
              path: 'buy',
              factory: $BuySubscriptionRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/training',
          factory: $TrainingCenterRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/road_cameras',
          factory: $RoadCameraRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/legalHelp',
          factory: $LegalHelpRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/discounts',
          factory: $DiscountRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'details',
              factory: $DiscountDetailsRouteExtension._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: '/information',
          factory: $InformationRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/service_book',
          factory: $ServiceBookRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'service',
              factory: $VehicleServicesRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'engine_oil',
                  factory: $EngineOilChangesRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      factory: $EditEngineOilChangeRouteExtension._fromState,
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'transmission_oil',
                  factory: $TransmissionOilChangesRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      factory:
                          $EditTransmissionOilChangeRouteExtension._fromState,
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'tyres',
                  factory: $TyresRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'new_tyre',
                      factory: $NewTyresRouteExtension._fromState,
                      routes: [
                        GoRouteData.$route(
                          path: 'edit',
                          factory: $EditNewTyreRouteExtension._fromState,
                        ),
                      ],
                    ),
                    GoRouteData.$route(
                      path: 'tyre_swap',
                      factory: $TyreSwapsRouteExtension._fromState,
                      routes: [
                        GoRouteData.$route(
                          path: 'edit',
                          factory: $EditTyreSwapRouteExtension._fromState,
                        ),
                      ],
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'other',
                  factory: $OtherServicesRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      factory: $EditOtherServiceRouteExtension._fromState,
                    ),
                  ],
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'fuel',
              factory: $RefuelsRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: $EditRefuelRouteExtension._fromState,
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'annual_inspection',
              factory: $AnnualInspectionRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: $EditAnnualInspectionRouteExtension._fromState,
                ),
              ],
            ),
          ],
        ),
      ],
    );

extension $MainRouteExtension on MainRoute {
  static MainRoute _fromState(GoRouterState state) => const MainRoute();
}

extension $HomeRouteExtension on HomeRoute {
  static HomeRoute _fromState(GoRouterState state) => const HomeRoute();

  String get location => GoRouteData.$location(
        '/home',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ProfileRouteExtension on ProfileRoute {
  static ProfileRoute _fromState(GoRouterState state) => const ProfileRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditProfileRouteExtension on EditProfileRoute {
  static EditProfileRoute _fromState(GoRouterState state) =>
      const EditProfileRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>/edit',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditPasswordRouteExtension on EditPasswordRoute {
  static EditPasswordRoute _fromState(GoRouterState state) =>
      const EditPasswordRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>/password',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $NotificationSettingsRouteExtension on NotificationSettingsRoute {
  static NotificationSettingsRoute _fromState(GoRouterState state) =>
      const NotificationSettingsRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>/notification_settings',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MyVehiclesRouteExtension on MyVehiclesRoute {
  static MyVehiclesRoute _fromState(GoRouterState state) =>
      const MyVehiclesRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>/vehicles',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $NewVehicleRouteExtension on NewVehicleRoute {
  static NewVehicleRoute _fromState(GoRouterState state) =>
      const NewVehicleRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>/vehicles/new',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditVehicleRouteExtension on EditVehicleRoute {
  static EditVehicleRoute _fromState(GoRouterState state) => EditVehicleRoute(
        id: int.parse(state.uri.queryParameters['id']!),
      );

  String get location => GoRouteData.$location(
        '/home/<USER>/vehicles/edit',
        queryParams: {
          'id': id.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $NotificationRouteExtension on NotificationRoute {
  static NotificationRoute _fromState(GoRouterState state) =>
      const NotificationRoute();

  String get location => GoRouteData.$location(
        '/home/<USER>',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $SubscriptionDetailsRouteExtension on SubscriptionDetailsRoute {
  static SubscriptionDetailsRoute _fromState(GoRouterState state) =>
      SubscriptionDetailsRoute(
        subscriptionId:
            int.parse(state.uri.queryParameters['subscription-id']!),
      );

  String get location => GoRouteData.$location(
        '/home/<USER>',
        queryParams: {
          'subscription-id': subscriptionId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ParkingRouteExtension on ParkingRoute {
  static ParkingRoute _fromState(GoRouterState state) => const ParkingRoute();

  String get location => GoRouteData.$location(
        '/parking',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $RequestParkingRouteExtension on RequestParkingRoute {
  static RequestParkingRoute _fromState(GoRouterState state) =>
      RequestParkingRoute(
        $extra: state.extra as ParkingRequest?,
      );

  String get location => GoRouteData.$location(
        '/parking/request',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $RoadAssistanceRouteExtension on RoadAssistanceRoute {
  static RoadAssistanceRoute _fromState(GoRouterState state) =>
      const RoadAssistanceRoute();

  String get location => GoRouteData.$location(
        '/road_assistance',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $RequestRoadAssistanceRouteExtension on RequestRoadAssistanceRoute {
  static RequestRoadAssistanceRoute _fromState(GoRouterState state) =>
      const RequestRoadAssistanceRoute();

  String get location => GoRouteData.$location(
        '/road_assistance/request',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $RequestRoadAssistanceOtherRouteExtension
    on RequestRoadAssistanceOtherRoute {
  static RequestRoadAssistanceOtherRoute _fromState(GoRouterState state) =>
      const RequestRoadAssistanceOtherRoute();

  String get location => GoRouteData.$location(
        '/road_assistance/request_other',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $TollRouteExtension on TollRoute {
  static TollRoute _fromState(GoRouterState state) => const TollRoute();

  String get location => GoRouteData.$location(
        '/toll',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CheckTollRouteExtension on CheckTollRoute {
  static CheckTollRoute _fromState(GoRouterState state) =>
      const CheckTollRoute();

  String get location => GoRouteData.$location(
        '/toll/check',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $BuyTollRouteExtension on BuyTollRoute {
  static BuyTollRoute _fromState(GoRouterState state) => const BuyTollRoute();

  String get location => GoRouteData.$location(
        '/toll/buy',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MotRouteExtension on MotRoute {
  static MotRoute _fromState(GoRouterState state) => const MotRoute();

  String get location => GoRouteData.$location(
        '/mot',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MotRequestRouteExtension on MotRequestRoute {
  static MotRequestRoute _fromState(GoRouterState state) =>
      const MotRequestRoute();

  String get location => GoRouteData.$location(
        '/mot/request',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $OtherRouteExtension on OtherRoute {
  static OtherRoute _fromState(GoRouterState state) => const OtherRoute();

  String get location => GoRouteData.$location(
        '/other',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $SubscriptionRouteExtension on SubscriptionRoute {
  static SubscriptionRoute _fromState(GoRouterState state) =>
      const SubscriptionRoute();

  String get location => GoRouteData.$location(
        '/subscription',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ActivateSubscriptionRouteExtension on ActivateSubscriptionRoute {
  static ActivateSubscriptionRoute _fromState(GoRouterState state) =>
      const ActivateSubscriptionRoute();

  String get location => GoRouteData.$location(
        '/subscription/activate',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $BuySubscriptionRouteExtension on BuySubscriptionRoute {
  static BuySubscriptionRoute _fromState(GoRouterState state) =>
      const BuySubscriptionRoute();

  String get location => GoRouteData.$location(
        '/subscription/buy',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $TrainingCenterRouteExtension on TrainingCenterRoute {
  static TrainingCenterRoute _fromState(GoRouterState state) =>
      const TrainingCenterRoute();

  String get location => GoRouteData.$location(
        '/training',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $RoadCameraRouteExtension on RoadCameraRoute {
  static RoadCameraRoute _fromState(GoRouterState state) =>
      const RoadCameraRoute();

  String get location => GoRouteData.$location(
        '/road_cameras',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $LegalHelpRouteExtension on LegalHelpRoute {
  static LegalHelpRoute _fromState(GoRouterState state) =>
      const LegalHelpRoute();

  String get location => GoRouteData.$location(
        '/legalHelp',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DiscountRouteExtension on DiscountRoute {
  static DiscountRoute _fromState(GoRouterState state) => const DiscountRoute();

  String get location => GoRouteData.$location(
        '/discounts',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DiscountDetailsRouteExtension on DiscountDetailsRoute {
  static DiscountDetailsRoute _fromState(GoRouterState state) =>
      DiscountDetailsRoute(
        $extra: state.extra as DiscountItem,
      );

  String get location => GoRouteData.$location(
        '/discounts/details',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $InformationRouteExtension on InformationRoute {
  static InformationRoute _fromState(GoRouterState state) =>
      const InformationRoute();

  String get location => GoRouteData.$location(
        '/information',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ServiceBookRouteExtension on ServiceBookRoute {
  static ServiceBookRoute _fromState(GoRouterState state) =>
      const ServiceBookRoute();

  String get location => GoRouteData.$location(
        '/service_book',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $VehicleServicesRouteExtension on VehicleServicesRoute {
  static VehicleServicesRoute _fromState(GoRouterState state) =>
      VehicleServicesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EngineOilChangesRouteExtension on EngineOilChangesRoute {
  static EngineOilChangesRoute _fromState(GoRouterState state) =>
      EngineOilChangesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/engine_oil',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditEngineOilChangeRouteExtension on EditEngineOilChangeRoute {
  static EditEngineOilChangeRoute _fromState(GoRouterState state) =>
      EditEngineOilChangeRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as EngineOilData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/service/engine_oil/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $TransmissionOilChangesRouteExtension on TransmissionOilChangesRoute {
  static TransmissionOilChangesRoute _fromState(GoRouterState state) =>
      TransmissionOilChangesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/transmission_oil',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditTransmissionOilChangeRouteExtension
    on EditTransmissionOilChangeRoute {
  static EditTransmissionOilChangeRoute _fromState(GoRouterState state) =>
      EditTransmissionOilChangeRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as TransmissionOilData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/service/transmission_oil/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $TyresRouteExtension on TyresRoute {
  static TyresRoute _fromState(GoRouterState state) => TyresRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/tyres',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $NewTyresRouteExtension on NewTyresRoute {
  static NewTyresRoute _fromState(GoRouterState state) => NewTyresRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/tyres/new_tyre',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditNewTyreRouteExtension on EditNewTyreRoute {
  static EditNewTyreRoute _fromState(GoRouterState state) => EditNewTyreRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as TyreData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/service/tyres/new_tyre/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $TyreSwapsRouteExtension on TyreSwapsRoute {
  static TyreSwapsRoute _fromState(GoRouterState state) => TyreSwapsRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/tyres/tyre_swap',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditTyreSwapRouteExtension on EditTyreSwapRoute {
  static EditTyreSwapRoute _fromState(GoRouterState state) => EditTyreSwapRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as TyreSwapData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/service/tyres/tyre_swap/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $OtherServicesRouteExtension on OtherServicesRoute {
  static OtherServicesRoute _fromState(GoRouterState state) =>
      OtherServicesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/service/other',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditOtherServiceRouteExtension on EditOtherServiceRoute {
  static EditOtherServiceRoute _fromState(GoRouterState state) =>
      EditOtherServiceRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as VehicleServiceData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/service/other/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $RefuelsRouteExtension on RefuelsRoute {
  static RefuelsRoute _fromState(GoRouterState state) => RefuelsRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/fuel',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditRefuelRouteExtension on EditRefuelRoute {
  static EditRefuelRoute _fromState(GoRouterState state) => EditRefuelRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as RefuelData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/fuel/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $AnnualInspectionRouteExtension on AnnualInspectionRoute {
  static AnnualInspectionRoute _fromState(GoRouterState state) =>
      AnnualInspectionRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  String get location => GoRouteData.$location(
        '/service_book/annual_inspection',
        queryParams: {
          'vehicle-id': vehicleId.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditAnnualInspectionRouteExtension on EditAnnualInspectionRoute {
  static EditAnnualInspectionRoute _fromState(GoRouterState state) =>
      EditAnnualInspectionRoute(
        vehicleId: _$convertMapValue(
            'vehicle-id', state.uri.queryParameters, int.parse),
        $extra: state.extra as AnnualInspectionData?,
      );

  String get location => GoRouteData.$location(
        '/service_book/annual_inspection/edit',
        queryParams: {
          if (vehicleId != null) 'vehicle-id': vehicleId!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

T? _$convertMapValue<T>(
  String key,
  Map<String, String> map,
  T Function(String) converter,
) {
  final value = map[key];
  return value == null ? null : converter(value);
}
