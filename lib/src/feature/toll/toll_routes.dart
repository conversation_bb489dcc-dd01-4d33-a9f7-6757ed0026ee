import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_screen.dart';
import 'package:sba/src/feature/toll/check/toll_check_screen.dart';
import 'package:sba/src/feature/toll/view/toll_screen.dart';

class TollRoute extends GoRouteData {
  const TollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const TollScreen();
  }
}

class CheckTollRoute extends GoRouteData {
  const CheckTollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const TollCheckScreen();
  }
}

class BuyTollRoute extends GoRouteData {
  const BuyTollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TollBuyScreen();
  }
}
