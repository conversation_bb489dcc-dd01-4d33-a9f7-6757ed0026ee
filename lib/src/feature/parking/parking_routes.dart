import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/parking/request/request_parking_screen.dart';
import 'package:sba/src/feature/parking/view/parking_screen.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';

class ParkingRoute extends GoRouteData {
  const ParkingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ParkingScreen();
  }
}

class RequestParkingRoute extends GoRouteData {
  const RequestParkingRoute({this.$extra});

  final ParkingRequest? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return RequestParkingScreen(
      args: RequestParkingScreenArgs(request: $extra),
    );
  }
}
