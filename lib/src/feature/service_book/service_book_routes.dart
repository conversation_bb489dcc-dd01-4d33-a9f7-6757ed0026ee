import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/service_book/engine_oil/edit/edit_engine_oil_screen.dart';
import 'package:sba/src/feature/service_book/engine_oil/view/engine_oil_screen.dart';
import 'package:sba/src/feature/service_book/fuel/edit/edit_fuel_screen.dart';
import 'package:sba/src/feature/service_book/fuel/view/fuel_screen.dart';
import 'package:sba/src/feature/service_book/mot/edit/edit_mot_service_screen.dart';
import 'package:sba/src/feature/service_book/mot/view/mot_service_screen.dart';
import 'package:sba/src/feature/service_book/other_service/edit/edit_other_service_screen.dart';
import 'package:sba/src/feature/service_book/other_service/view/other_service_screen.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/edit_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/buy_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/edit_tire_swap_screen.dart';
import 'package:sba/src/feature/service_book/tires/swap/view/swap_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/view/tyres_screen.dart';
import 'package:sba/src/feature/service_book/transmission_oil/edit/edit_transmission_oil_screen.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/transmission_oil_screen.dart';
import 'package:sba/src/feature/service_book/vehicle_service/vehicle_service_screen.dart';
import 'package:sba/src/feature/service_book/view/service_book_screen.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';

class ServiceBookRoute extends GoRouteData {
  const ServiceBookRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ServiceBookScreen();
  }
}

class VehicleServicesRoute extends GoRouteData {
  const VehicleServicesRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return VehicleServiceScreen(
      args: VehicleServiceScreenArgs(vehicleId: vehicleId),
    );
  }
}

class EngineOilChangesRoute extends GoRouteData {
  const EngineOilChangesRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EngineOilScreen(args: EngineOilScreenArgs(vehicleId: vehicleId));
  }
}

class EditEngineOilChangeRoute extends GoRouteData {
  EditEngineOilChangeRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final EngineOilData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditEngineOilScreen(
      args: EditEngineOilScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class TransmissionOilChangesRoute extends GoRouteData {
  const TransmissionOilChangesRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TransmissionOilScreen(
      args: TransmissionOilScreenArgs(vehicleId: vehicleId),
    );
  }
}

class EditTransmissionOilChangeRoute extends GoRouteData {
  EditTransmissionOilChangeRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final TransmissionOilData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditTransmissionOilScreen(
      args: EditTransmissionOilScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class OtherServicesRoute extends GoRouteData {
  const OtherServicesRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return OtherServiceScreen(
      args: OtherServiceScreenArgs(vehicleId: vehicleId),
    );
  }
}

class EditOtherServiceRoute extends GoRouteData {
  EditOtherServiceRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final VehicleServiceData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditOtherServiceScreen(
      args: EditOtherServiceScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class TyresRoute extends GoRouteData {
  const TyresRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TyresScreen(args: TyresScreenArgs(vehicleId: vehicleId));
  }
}

class NewTyresRoute extends GoRouteData {
  const NewTyresRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return BuyTireScreen(args: BuyTireScreenArgs(vehicleId: vehicleId));
  }
}

class EditNewTyreRoute extends GoRouteData {
  EditNewTyreRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final TyreData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditTireScreen(
      args: EditTireScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class TyreSwapsRoute extends GoRouteData {
  const TyreSwapsRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SwapTireScreen(args: SwapTireScreenArgs(vehicleId: vehicleId));
  }
}

class EditTyreSwapRoute extends GoRouteData {
  EditTyreSwapRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final TyreSwapData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditTireSwapScreen(
      args: EditTireSwapScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class RefuelsRoute extends GoRouteData {
  const RefuelsRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return FuelScreen(args: FuelScreenArgs(vehicleId: vehicleId));
  }
}

class EditRefuelRoute extends GoRouteData {
  EditRefuelRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final RefuelData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditFuelScreen(
      args: EditFuelScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}

class AnnualInspectionRoute extends GoRouteData {
  const AnnualInspectionRoute({required this.vehicleId});

  final int vehicleId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return MotServiceScreen(args: MotServiceScreenArgs(vehicleId: vehicleId));
  }
}

class EditAnnualInspectionRoute extends GoRouteData {
  EditAnnualInspectionRoute({this.vehicleId, this.$extra});

  final int? vehicleId;
  final AnnualInspectionData? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditMotServiceScreen(
      args: EditMotServiceScreenArgs(vehicleId: vehicleId, data: $extra),
    );
  }
}
