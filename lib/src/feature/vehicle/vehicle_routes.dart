import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_screen.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_screen.dart';

class MyVehiclesRoute extends GoRouteData {
  const MyVehiclesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const VehiclesScreen();
  }
}

class NewVehicleRoute extends GoRouteData {
  const NewVehicleRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditVehicleScreen();
  }
}

class EditVehicleRoute extends GoRouteData {
  const EditVehicleRoute({required this.id});

  final int id;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditVehicleScreen(
      args: EditVehicleScreenArgs(id: id),
    );
  }
}
