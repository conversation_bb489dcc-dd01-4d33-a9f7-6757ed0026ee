import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/road_assistance_api.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/model/types/road_assistance_request_status_type.dart';

const _tag = 'RoadAssistanceRepository';

final class RoadAssistanceRepository {
  RoadAssistanceRepository({
    required GeneralRepository generalRepository,
    required AuthRepository authRepository,
    required RoadAssistanceApi api,
    required Logger logger,
  })  : _generalRepository = generalRepository,
        _authRepository = authRepository,
        _api = api,
        _logger = logger {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _requestsStream = _cachedResponse
        .distinct()
        .switchMap(
          (e) => e != null
              ? Stream.value(e)
              : _authRepository.loggedInStream
                  .where((e) => e)
                  .asyncMap((e) => _getRequests())
                  .doOnData(_cachedResponse.add),
        )
        .publishReplay(maxSize: 1)
      ..connect();
  }

  final GeneralRepository _generalRepository;
  final AuthRepository _authRepository;
  final RoadAssistanceApi _api;
  final Logger _logger;
  final _cachedResponse =
      BehaviorSubject<Result<List<RoadAssistanceRequest>>?>.seeded(null);
  late final Stream<Result<List<RoadAssistanceRequest>>> _requestsStream;

  Stream<Result<List<RoadAssistanceRequest>>> get requests => _requestsStream;

  Stream<int> get requestCount =>
      requests.map((e) => e.maybeValue?.length ?? 0);

  Future<RoadAssistanceRequest?> getRequest(int id) => _requestsStream
      .map((e) => e.maybeValue)
      .map((e) => e?.firstWhereOrNull((i) => i.id == id))
      .first;

  Future<List<RoadAssistanceRequest>?> getRequests() =>
      _requestsStream.map((e) => e.maybeValue).first;

  Future<Result<List<RoadAssistanceRequest>>> _getRequests() async {
    try {
      final result = await callWithResult(_api.getRequests);

      final reasons = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository
              .getRoadAssistanceReasons()
              .then((e) => e.maybeValue)
          : null;
      final brands = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository.getCarBrands().then((e) => e.maybeValue)
          : null;

      final mapped = result.listMap(
        (data) => RoadAssistanceRequest.fromData(
          dto: data,
          reasons: reasons,
          brands: brands,
        ),
        growable: true,
      );

      return mapped;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> addRequestForCurrentUser(
    RoadAssistanceRequest data,
  ) async {
    try {
      final result = await callWithResult(
        () => _api.addRequestForCurrentUser(data: data.toDto()),
      );

      if (result.isSuccess) {
        final dto = result.maybeValue!;
        final createdRequest = data.copyWith(
          id: dto.id,
          created: dto.created,
          smartViewUrl: dto.smartViewUrl,
          estimatedArrivalTime: dto.estimatedArrivalTime,
          status: RoadAssistanceRequestStatusType.fromCode(dto.status ?? 0),
        );

        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.add(createdRequest);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> addRequestForOtherDriver(
    RoadAssistanceRequest data,
  ) async {
    try {
      final result = await callWithResult(
        () => _api.addRequestForOtherDriver(data: data.toDto()),
      );

      if (result.isSuccess) {
        final dto = result.maybeValue!;
        final createdRequest = data.copyWith(
          id: dto.id,
          created: dto.created,
          smartViewUrl: dto.smartViewUrl,
          estimatedArrivalTime: dto.estimatedArrivalTime,
          status: RoadAssistanceRequestStatusType.fromCode(dto.status ?? 0),
        );

        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.add(createdRequest);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> cancelRequest(RoadAssistanceRequest data) async {
    try {
      if (data.id == null) {
        return Result.otherError('Request id is null');
      }

      final result = await callWithResult(
        () => _api.cancelRequest(id: data.id!),
      );

      if (result.isSuccess) {
        final cancelled = data.copyWith(
          status: RoadAssistanceRequestStatusType.declinedByUser,
        );

        var list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list = list?.replaceWhereOrAdd((e) => e.id == cancelled.id, cancelled);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
