import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:sba/src/api/model/road_assistance/road_assistance_request_dto.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_drive_type.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_transmission_type.dart';
import 'package:sba/src/repository/road_assistance/model/types/road_assistance_request_status_type.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class RoadAssistanceRequest {
  const RoadAssistanceRequest({
    this.coordinates,
    required this.plateNumber,
    this.reason,
    this.carBrand,
    this.carModel,
    this.carTransmissionType,
    this.carDriveType,
    this.id,
    this.driverName,
    this.driverPhone,
    this.note,
    this.created,
    this.smartViewUrl,
    this.estimatedArrivalTime,
    this.status,
  });

  factory RoadAssistanceRequest.fromData({
    required RoadAssistanceRequestDto dto,
    List<RoadAssistanceReason>? reasons,
    List<CarBrand>? brands,
  }) {
    final reason = reasons?.firstWhereOrNull((it) => it.id == dto.reasonId);
    final brand = brands?.firstWhereOrNull((it) => it.id == dto.carMakerId);
    final model =
        brand?.models.firstWhereOrNull((it) => it.id == dto.carModelId);

    return RoadAssistanceRequest(
      id: dto.id,
      coordinates: dto.latitude != null && dto.longitude != null
          ? LatLng(dto.latitude!, dto.longitude!)
          : null,
      reason: reason,
      driverName: dto.driverName,
      driverPhone: dto.driverPhone,
      carBrand: brand,
      carModel: model,
      plateNumber: dto.carDkn,
      carTransmissionType:
          CarTransmissionType.fromCode(dto.carTransmissionType),
      carDriveType: CarDriveType.fromCode(dto.carDriveType),
      note: dto.note,
      created: dto.created,
      smartViewUrl: dto.smartViewUrl,
      estimatedArrivalTime: dto.estimatedArrivalTime,
      status: dto.status != null
          ? RoadAssistanceRequestStatusType.fromCode(dto.status!)
          : null,
    );
  }

  factory RoadAssistanceRequest.fake() => RoadAssistanceRequest(
        id: 1,
        coordinates: const LatLng(42.136097, 24.742168),
        driverName: BoneMock.name,
        driverPhone: BoneMock.phone,
        plateNumber: 'PB1234AA',
        carTransmissionType: CarTransmissionType.manual,
        carDriveType: CarDriveType.fwd,
        note: BoneMock.words(5),
        created: DateTime.now(),
        estimatedArrivalTime: DateTime.now().add(const Duration(hours: 2)),
        status: RoadAssistanceRequestStatusType.declinedByUser,
      );

  factory RoadAssistanceRequest.empty() => const RoadAssistanceRequest(
        plateNumber: '',
        carTransmissionType: CarTransmissionType.unknown,
        carDriveType: CarDriveType.unknown,
      );

  final int? id;
  final LatLng? coordinates;
  final RoadAssistanceReason? reason;
  final String? driverName;
  final String? driverPhone;
  final CarBrand? carBrand;
  final CarModel? carModel;
  final String plateNumber;
  final CarTransmissionType? carTransmissionType;
  final CarDriveType? carDriveType;
  final String? note;
  final DateTime? created;
  final String? smartViewUrl;
  final DateTime? estimatedArrivalTime;
  final RoadAssistanceRequestStatusType? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RoadAssistanceRequest &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  RoadAssistanceRequest copyWith({
    int? id,
    LatLng? coordinates,
    RoadAssistanceReason? reason,
    String? driverName,
    String? driverPhone,
    CarBrand? carBrand,
    CarModel? carModel,
    String? plateNumber,
    CarTransmissionType? carTransmissionType,
    CarDriveType? carDriveType,
    String? note,
    DateTime? created,
    String? smartViewUrl,
    DateTime? estimatedArrivalTime,
    RoadAssistanceRequestStatusType? status,
  }) {
    return RoadAssistanceRequest(
      id: id ?? this.id,
      coordinates: coordinates ?? this.coordinates,
      reason: reason ?? this.reason,
      driverName: driverName ?? this.driverName,
      driverPhone: driverPhone ?? this.driverPhone,
      carBrand: carBrand ?? this.carBrand,
      carModel: carModel ?? this.carModel,
      plateNumber: plateNumber ?? this.plateNumber,
      carTransmissionType: carTransmissionType ?? this.carTransmissionType,
      carDriveType: carDriveType ?? this.carDriveType,
      note: note ?? this.note,
      created: created ?? this.created,
      smartViewUrl: smartViewUrl ?? this.smartViewUrl,
      estimatedArrivalTime: estimatedArrivalTime ?? this.estimatedArrivalTime,
      status: status ?? this.status,
    );
  }
}

extension RoadAssistanceRequestExtension on RoadAssistanceRequest {
  RoadAssistanceRequestDto toDto() => RoadAssistanceRequestDto(
        id: id,
        latitude: coordinates?.latitude,
        longitude: coordinates?.longitude,
        reasonId: reason?.id ?? 0,
        driverName: driverName,
        driverPhone: driverPhone,
        carMakerId: carBrand?.id ?? 0,
        carModelId: carModel?.id ?? 0,
        carDkn: plateNumber,
        carTransmissionType: carTransmissionType?.code ?? 0,
        carDriveType: carDriveType?.code ?? 0,
        note: note ?? 'string',
        created: created,
        smartViewUrl: smartViewUrl,
        estimatedArrivalTime: estimatedArrivalTime,
        status: status?.code,
      );
}
