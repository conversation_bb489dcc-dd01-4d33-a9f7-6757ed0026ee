import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum SubscriptionPaymentType {
  delivery,
  card,
  bank,
}

extension SubscriptionPaymentTypeExtension on SubscriptionPaymentType {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionPaymentType.delivery =>
          context.l10n.form_type_option_payment_delivery,
        SubscriptionPaymentType.card =>
          context.l10n.form_type_option_payment_card,
        SubscriptionPaymentType.bank =>
          context.l10n.form_type_option_payment_bank,
      };
}
