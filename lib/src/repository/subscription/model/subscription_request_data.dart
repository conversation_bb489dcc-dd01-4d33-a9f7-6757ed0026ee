import 'package:collection/collection.dart';
import 'package:sba/src/api/model/request/delivery_request_dto.dart';
import 'package:sba/src/api/model/subscription/subscription_order_request_dto.dart';
import 'package:sba/src/delivery/model/address_delivery_data.dart';
import 'package:sba/src/delivery/model/delivery_office.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_delivery_type.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_order_status.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class SubscriptionRequestData {
  SubscriptionRequestData({
    required this.id,
    required this.subscriptionData,
    required this.company,
    required this.eik,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.place,
    required this.address,
    required this.vehicle,
    required this.status,
    required this.additionalPackage,
    required this.deliveryType,
    required this.deliveryOffice,
    required this.addressDeliveryData,
  });

  factory SubscriptionRequestData.fromData({
    required SubscriptionOrderRequestDto dto,
    List<SubscriptionProductData>? products,
    List<Place>? places,
    List<VehicleData>? vehicles,
  }) {
    final subscription = products?.firstWhereOrNull(
      (e) =>
          e.category?.code == dto.cardCategory && e.type?.code == dto.cardType,
    );

    return SubscriptionRequestData(
      id: dto.id,
      subscriptionData: subscription,
      company: dto.firstName,
      eik: dto.lastName,
      firstName: dto.firstName,
      lastName: dto.lastName,
      email: dto.email,
      phone: dto.mobile,
      place: places?.firstWhereOrNull((e) => e.id == dto.cityId),
      address: dto.address,
      vehicle: vehicles?.firstWhereOrNull((e) => e.id == dto.carId),
      status: dto.status == null
          ? null
          : SubscriptionOrderStatus.fromCode(dto.status!),
      additionalPackage: subscription?.packages
          .where((e) => dto.selectedPackageIds?.contains(e.id) ?? false)
          .toList(),
      deliveryType:
          SubscriptionDeliveryType.fromCode(dto.delivery.deliveryTo) ??
              SubscriptionDeliveryType.none,
      deliveryOffice: DeliveryOffice.fromRequestDto(dto.delivery),
      addressDeliveryData: AddressDeliveryData.fromDto(dto.delivery),
    );
  }

  factory SubscriptionRequestData.fake() => SubscriptionRequestData(
        id: 0,
        subscriptionData: SubscriptionProductData.fake(),
        company: BoneMock.name,
        eik: BoneMock.title,
        firstName: BoneMock.name,
        lastName: BoneMock.name,
        email: BoneMock.email,
        phone: BoneMock.phone,
        place: null,
        address: BoneMock.address,
        vehicle: VehicleData.fake(),
        status: SubscriptionOrderStatus.completed,
        additionalPackage: [],
        deliveryType: SubscriptionDeliveryType.none,
        deliveryOffice: null,
        addressDeliveryData: null,
      );

  final int? id;
  final SubscriptionProductData? subscriptionData;
  final String? company;
  final String? eik;
  final String? firstName;
  final String? lastName;
  final String email;
  final String phone;
  final Place? place;
  final String address;
  final VehicleData? vehicle;
  final SubscriptionOrderStatus? status;
  final List<SubscriptionPackage>? additionalPackage;
  final SubscriptionDeliveryType deliveryType;
  final DeliveryOffice? deliveryOffice;
  final AddressDeliveryData? addressDeliveryData;

  SubscriptionRequestData copyWith({
    int? id,
    SubscriptionProductData? subscriptionData,
    String? company,
    String? eik,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    Place? place,
    String? address,
    VehicleData? vehicle,
    SubscriptionOrderStatus? status,
    List<SubscriptionPackage>? additionalPackage,
    SubscriptionDeliveryType? deliveryType,
    DeliveryOffice? deliveryOffice,
    AddressDeliveryData? addressDeliveryData,
  }) {
    return SubscriptionRequestData(
      id: id ?? this.id,
      subscriptionData: subscriptionData ?? this.subscriptionData,
      company: company ?? this.company,
      eik: eik ?? this.eik,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      place: place ?? this.place,
      address: address ?? this.address,
      vehicle: vehicle ?? this.vehicle,
      status: status ?? this.status,
      additionalPackage: additionalPackage ?? this.additionalPackage,
      deliveryType: deliveryType ?? this.deliveryType,
      deliveryOffice: deliveryOffice ?? this.deliveryOffice,
      addressDeliveryData: addressDeliveryData ?? this.addressDeliveryData,
    );
  }
}

extension SubscriptionRequestDataExtension on SubscriptionRequestData {
  SubscriptionOrderRequestDto toDto() => SubscriptionOrderRequestDto(
        id: null,
        carId: vehicle?.id ?? 0,
        oldCardId: null,
        firstName: company ?? firstName ?? '',
        lastName: eik ?? lastName ?? '',
        email: email,
        mobile: phone,
        cityId: place?.id ?? 0,
        address: address,
        cardCategory: subscriptionData?.category?.code ?? 0,
        cardType: subscriptionData?.type?.code ?? 0,
        status: status?.code ?? 0,
        note: '',
        selectedPackageIds: additionalPackage?.map((e) => e.id).toList() ?? [],
        delivery: DeliveryRequestDto(
          deliveryTo: deliveryType.code,
          speedyOfficeId: deliveryOffice?.id,
          speedyOffice: deliveryOffice?.name,
          cityId: addressDeliveryData?.city.id,
          city: addressDeliveryData?.city.name,
          complexId: addressDeliveryData?.complex?.id,
          complex: addressDeliveryData?.complex?.name,
          streetId: addressDeliveryData?.street.id,
          street: addressDeliveryData?.street.name,
          streetNumber: addressDeliveryData?.number,
          blockId: addressDeliveryData?.block?.id,
          block: addressDeliveryData?.block?.name,
          entranceNumber: addressDeliveryData?.entrance,
          floor: addressDeliveryData?.floor,
          apartmentNumber: addressDeliveryData?.apartment,
          note: addressDeliveryData?.note,
        ),
      );
}
