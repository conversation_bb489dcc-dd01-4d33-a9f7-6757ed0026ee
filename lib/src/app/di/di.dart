import 'package:app_links/app_links.dart';
import 'package:dio/dio.dart';
import 'package:elementary/elementary.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rx_shared_preferences/rx_shared_preferences.dart';
import 'package:sba/environment.dart';
import 'package:sba/src/api/account_api.dart';
import 'package:sba/src/api/avi_api.dart';
import 'package:sba/src/api/cards_api.dart';
import 'package:sba/src/api/client/client_factory.dart';
import 'package:sba/src/api/contacts_api.dart';
import 'package:sba/src/api/content_api.dart';
import 'package:sba/src/api/delivery_api.dart';
import 'package:sba/src/api/dsk_api.dart';
import 'package:sba/src/api/general_api.dart';
import 'package:sba/src/api/notification_api.dart';
import 'package:sba/src/api/parking_api.dart';
import 'package:sba/src/api/road_assistance_api.dart';
import 'package:sba/src/api/road_camera_api.dart';
import 'package:sba/src/api/service_book_api.dart';
import 'package:sba/src/api/token_api.dart';
import 'package:sba/src/api/toll_api.dart';
import 'package:sba/src/api/user_api.dart';
import 'package:sba/src/api/users_api.dart';
import 'package:sba/src/api/vehicle_api.dart';
import 'package:sba/src/common/extension/logger_extension.dart';
import 'package:sba/src/common/lifecycle_observer/lifecycle_observer.dart';
import 'package:sba/src/common/logging/app_error_handler.dart';
import 'package:sba/src/common/logging/rx_prefs_logger.dart';
import 'package:sba/src/common/sms_handler/sms_handler.dart';
import 'package:sba/src/common/utils/splash_screen.dart';
import 'package:sba/src/delivery/repository/delivery_repository.dart';
import 'package:sba/src/localization/bg_messages.dart';
import 'package:sba/src/payment/payment_processor.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/avi/avi_repository.dart';
import 'package:sba/src/repository/contact/contact_repository.dart';
import 'package:sba/src/repository/content/content_repository.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/notifications/notification_repository.dart';
import 'package:sba/src/repository/parking/parking_repository.dart';
import 'package:sba/src/repository/road_assistance/road_assistance_repository.dart';
import 'package:sba/src/repository/road_camera/road_camera_repository.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';
import 'package:sba/src/repository/toll/toll_repository.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';
import 'package:sms_sender_background/sms_sender.dart';
import 'package:timeago/timeago.dart' as timeago;

final _di = GetIt.I;

T get<T extends Object>() => _di.get<T>();

Future<void> setup() {
  final binding = WidgetsFlutterBinding.ensureInitialized();
  // preserve splash screen
  SplashScreen.preserve(widgetsBinding: binding);

  // logger config
  Logger.level = levelFromCodeBuilder(Environment().logLevel);
  Logger.defaultPrinter = () => PrettyPrinter(methodCount: 0);
  FlutterError.onError = (details) => Logger().e(
        'Error',
        error: details.exception,
        stackTrace: details.stack,
      );

  // localization config
  timeago.setLocaleMessages('bg', BgMessages());

  // one signal config
  OneSignal.Debug.setLogLevel(
    osLogLevelFromCodeBuilder(Environment().logLevel),
  );
  OneSignal.initialize(Environment().oneSignalAppId);

  // base injection
  _di
    ..registerSingleton<Logger>(Logger())
    ..registerSingleton<AppLinks>(AppLinks())
    ..registerSingleton<SmsHandler>(
      SmsHandler(
        logger: get(),
        sms: SmsSender(),
      ),
    )
    ..registerSingleton<LifecycleObserver>(
      LifecycleObserver(),
      dispose: (i) => i.dispose(),
    )
    ..registerSingleton<ErrorHandler>(AppErrorHandler(logger: get()))
    ..registerSingleton<RxSharedPreferences>(
      RxSharedPreferences(
        SharedPreferences.getInstance(),
        RxPrefsLogger(get()),
      ),
    );

  //auth
  final client = buildClient(logger: get(), baseUrl: Environment().baseUrl);

  _di
    ..registerSingleton<TokenApi>(TokenApi(client))
    ..registerSingleton<AccountApi>(AccountApi(client))
    ..registerSingleton<AuthRepository>(
      AuthRepository(get(), get(), get(), get()),
    )
    ..registerSingleton<Dio>(
      buildAuthenticatedClient(
        logger: get(),
        baseUrl: Environment().baseUrl,
        auth: get(),
      ),
    );

  // payment
  final paymentClient = buildPaymentClient(
    logger: get(),
    baseUrl: Environment().paymentUrl,
    username: Environment().paymentUsername,
    password: Environment().paymentPassword,
  );

  _di
    ..registerSingleton<DSKApi>(DSKApi(paymentClient))
    ..registerSingleton<PaymentProcessor>(
      PaymentProcessor(
        logger: get(),
        dskApi: get(),
        appLinks: get(),
        lifecycleObserver: get(),
      ),
    )

    //api

    ..registerSingleton<GeneralApi>(
      GeneralApi(get()),
    )
    ..registerSingleton<UserApi>(
      UserApi(get()),
    )
    ..registerSingleton<NotificationApi>(
      NotificationApi(get()),
    )
    ..registerSingleton<UsersApi>(
      UsersApi(get()),
    )
    ..registerSingleton<VehicleApi>(
      VehicleApi(get()),
    )
    ..registerSingleton<RoadCameraApi>(
      RoadCameraApi(get()),
    )
    ..registerSingleton<ContactsApi>(
      ContactsApi(get()),
    )
    ..registerSingleton<ContentApi>(
      ContentApi(get()),
    )
    ..registerSingleton<ParkingApi>(
      ParkingApi(get()),
    )
    ..registerSingleton<ServiceBookApi>(
      ServiceBookApi(get()),
    )
    ..registerSingleton<TollApi>(
      TollApi(get()),
    )
    ..registerSingleton<CardsApi>(
      CardsApi(get()),
    )
    ..registerSingleton<DeliveryApi>(
      DeliveryApi(get()),
    )
    ..registerSingleton<AviApi>(
      AviApi(get()),
    )
    ..registerSingleton<RoadAssistanceApi>(
      RoadAssistanceApi(get()),
    )

    // repository
    ..registerLazySingleton<GeneralRepository>(
      () => GeneralRepository(logger: get(), api: get()),
    )
    ..registerLazySingleton<UserRepository>(
      () => UserRepository(
        logger: get(),
        preferences: get(),
        authRepository: get(),
        userApi: get(),
        usersApi: get(),
      ),
    )
    ..registerLazySingleton<NotificationRepository>(
      () => NotificationRepository(
        logger: get(),
        userRepository: get(),
        authRepository: get(),
        notificationApi: get(),
        lifecycleObserver: get(),
      ),
    )
    ..registerLazySingleton<VehicleRepository>(
      () => VehicleRepository(
        logger: get(),
        generalRepository: get(),
        authRepository: get(),
        userRepository: get(),
        api: get(),
      ),
    )
    ..registerLazySingleton<RoadCameraRepository>(
      () => RoadCameraRepository(api: get(), logger: get()),
    )
    ..registerLazySingleton<ContactRepository>(
      () => ContactRepository(logger: get(), api: get()),
    )
    ..registerLazySingleton<ContentRepository>(
      () => ContentRepository(logger: get(), api: get()),
    )
    ..registerLazySingleton<ParkingRepository>(
      () => ParkingRepository(logger: get(), authRepository: get(), api: get()),
    )
    ..registerLazySingleton<ServiceBookRepository>(
      () => ServiceBookRepository(
        logger: get(),
        api: get(),
        generalRepository: get(),
      ),
    )
    ..registerLazySingleton<TollRepository>(
      () => TollRepository(
        logger: get(),
        tollApi: get(),
        userRepository: get(),
        authRepository: get(),
        vehicleRepository: get(),
        paymentProcessor: get(),
      ),
    )
    ..registerLazySingleton<SubscriptionRepository>(
      () => SubscriptionRepository(
        api: get(),
        userRepository: get(),
        authRepository: get(),
        vehicleRepository: get(),
        generalRepository: get(),
        logger: get(),
      ),
    )
    ..registerLazySingleton<DeliveryRepository>(
      () => DeliveryRepository(api: get(), logger: get()),
    )
    ..registerLazySingleton<AVIRepository>(
      () => AVIRepository(
        api: get(),
        logger: get(),
        userRepository: get(),
        authRepository: get(),
      ),
    )
    ..registerLazySingleton<RoadAssistanceRepository>(
      () => RoadAssistanceRepository(
        generalRepository: get(),
        authRepository: get(),
        api: get(),
        logger: get(),
      ),
    );

  return _di.allReady();
}
