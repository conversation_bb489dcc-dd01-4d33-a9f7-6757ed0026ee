import 'dart:async';

import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/ui/modal/loading_dialog.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:sba/src/ui/theme/ui_colors.dart';
import 'package:toastification/toastification.dart';

typedef MessageDialogBuilder = MessageDialog Function(BuildContext context);

BuildContext? _loadingDialogContext;

extension BuildContextExtension on BuildContext {
  NavigatorState get navigator => Navigator.of(this);

  NavigatorState get rootNavigator => Navigator.of(this, rootNavigator: true);

  FocusScopeNode get focus => FocusScope.of(this);


  Future<T?> showModalBottomSheet<T>({required WidgetBuilder builder}) =>
      showBarModalBottomSheet<T>(
        context: this,
        builder: builder,
        useRootNavigator: true,
        barrierColor: Colors.black54,
      );

  Future<void> showMessageDialog({required MessageDialogBuilder builder}) =>
      showDialog(
        context: this,
        builder: builder,
        barrierDismissible: false,
      );

  Future<void> showGeneralErrorDialog<S>({
    required Failure<S> failure,
    MessageDialog? Function(int code)? httpErrorBuilder,
    HttpCodeTextBuilder? codeTextBuilder,
  }) async {
    final dialog = switch (failure) {
      HttpError<S>(code: final code) => httpErrorBuilder?.call(code) ??
          MessageDialog.error(
            context: this,
            text: failure.failureLocalizedText(
              this,
              codeTextBuilder: codeTextBuilder,
            ),
          ),
      _ => MessageDialog.error(
          context: this,
          text: failure.failureLocalizedText(
            this,
            codeTextBuilder: codeTextBuilder,
          ),
        ),
    };

    return showMessageDialog(builder: (_) => dialog);
  }

  void showToast({
    required ToastificationType type,
    required String title,
    String? description,
  }) =>
      toastification.show(
        context: this,
        alignment: Alignment.bottomCenter,
        closeOnClick: true,
        dragToClose: true,
        type: type,
        style: ToastificationStyle.flat,
        showProgressBar: false,
        primaryColor: switch (type) {
          ToastificationType.info => UIColors.primary,
          ToastificationType.warning => UIColors.primary,
          ToastificationType.success => UIColors.green,
          ToastificationType.error => UIColors.red,
        },
        title: Text(title),
        description: description != null ? Text(description) : null,
        autoCloseDuration: const Duration(seconds: 3),
      );

  Future<void> showLoadingDialog() {
    if (_loadingDialogContext != null) {
      hideLoadingDialog();
    }

    final completer = Completer<void>();

    showDialog<void>(
      context: this,
      barrierDismissible: false,
      builder: (context) {
        _loadingDialogContext = context;
        if (!completer.isCompleted) completer.complete();
        return const LoadingDialog();
      },
    );

    return completer.future;
  }

  void hideLoadingDialog() {
    if (_loadingDialogContext?.mounted ?? false) {
      _loadingDialogContext?.navigator.pop();
    }

    _loadingDialogContext = null;
  }
}
