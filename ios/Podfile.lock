PODS:
  - app_links (0.0.2):
    - Flutter
  - Flutter (1.0.0)
  - flutter_compass (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - onesignal_flutter (5.3.4):
    - Flutter
    - OneSignalXCFramework (= 5.2.14)
  - OneSignalXCFramework (5.2.14):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.14)
  - OneSignalXCFramework/OneSignal (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.14):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.14)
  - OneSignalXCFramework/OneSignalExtension (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - Flutter (from `Flutter`)
  - flutter_compass (from `.symlinks/plugins/flutter_compass/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - OneSignalXCFramework

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  Flutter:
    :path: Flutter
  flutter_compass:
    :path: ".symlinks/plugins/flutter_compass/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_compass: b236ab69b61545cce89fd58527f401a7587d5cc1
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  location: 155caecf9da4f280ab5fe4a55f94ceccfab838f8
  onesignal_flutter: 3a2b51f9262c851166a882a553d71a8ccec4e29c
  OneSignalXCFramework: 7112f3e89563e41ebc23fe807788f11985ac541c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: ec13683566da18496fbc54c1e97805e348589fb6

COCOAPODS: 1.16.2
